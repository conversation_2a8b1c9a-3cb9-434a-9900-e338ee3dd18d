import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { chromium, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page } from 'playwright';

// Global browser manager
class PlaywrightManager {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;

  async connectToBrowser(wsEndpoint: string): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }

    this.browser = await chromium.connectOverCDP(wsEndpoint);
    this.context = this.browser.contexts()[0] || await this.browser.newContext();
    this.page = this.context.pages()[0] || await this.context.newPage();
  }

  async ensureConnected(): Promise<void> {
    if (!this.page) {
      throw new Error('No browser page available. Please connect to a browser first using connectBrowser tool.');
    }
  }

  getPage(): Page {
    if (!this.page) {
      throw new Error('No browser page available. Please connect to a browser first.');
    }
    return this.page;
  }

  async disconnect(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
      this.page = null;
    }
  }
}

const manager = new PlaywrightManager();

// 连接到浏览器工具
export const connectBrowser = createTool({
  id: 'connect-browser',
  description: 'Connect to a browser using WebSocket endpoint URL',
  inputSchema: z.object({
    wsEndpoint: z.string().describe('WebSocket endpoint URL to connect to the browser'),
  }),
  execute: async ({ context }) => {
    const { wsEndpoint } = context;
    console.log('wsEndpoint', wsEndpoint, context);
    try {
        await manager.connectToBrowser(wsEndpoint);
      return {
        success: true,
        message: `Successfully connected to browser at ${wsEndpoint}`,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to connect to browser: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

// 执行 window 全局方法工具
export const executeWindowMethod = createTool({
  id: 'execute-window-method',
  description: 'Execute a global method on the window object (e.g., alert, confirm, prompt)',
  inputSchema: z.object({
    method: z.string().describe('Window global method name to execute'),
    args: z.array(z.any()).optional().default([]).describe('Arguments to pass to the method'),
  }),
  execute: async ({ context }) => {
    const { method, args } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      const result = await page.evaluate(({ method, args }) => {
        if (typeof (window as any)[method] !== 'function') {
          throw new Error(`Method '${method}' is not available on window object`);
        }
        return (window as any)[method](...args);
      }, { method, args });

      return {
        success: true,
        result,
        message: `Successfully executed window.${method}()`,
      };
    } catch (error) {
      return {
        success: false,
        result: null,
        message: `Failed to execute window method: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

// 导航工具
export const navigateToUrl = createTool({
  id: 'navigate-to-url',
  description: 'Navigate to a specific URL in the browser',
  inputSchema: z.object({
    url: z.string().describe('URL to navigate to'),
  }),
  execute: async ({ context }) => {
    const { url } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      await page.goto(url);
      const currentUrl = page.url();

      return {
        success: true,
        message: `Successfully navigated to ${url}`,
        url: currentUrl,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to navigate: ${error instanceof Error ? error.message : String(error)}`,
        url: '',
      };
    }
  },
});

// 获取页面信息工具
export const getPageInfo = createTool({
  id: 'get-page-info',
  description: 'Get current page information including URL, title, and optionally element info',
  inputSchema: z.object({
    selector: z.string().optional().describe('CSS selector to get specific element info'),
  }),
  execute: async ({ context }) => {
    const { selector } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      let data: any = {
        url: page.url(),
        title: await page.title(),
      };

      if (selector) {
        const element = await page.$(selector);
        if (element) {
          const elementInfo = await element.evaluate((el) => ({
            tagName: el.tagName,
            textContent: el.textContent?.trim(),
            innerHTML: el.innerHTML,
            attributes: Object.fromEntries(
              Array.from(el.attributes).map((attr: any) => [attr.name, attr.value])
            ),
          }));
          data.element = elementInfo;
        } else {
          data.element = null;
        }
      }

      return {
        success: true,
        data,
        message: 'Successfully retrieved page information',
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: `Failed to get page info: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

export const getElementPlusVue = createTool({
  id: 'get-element-plus-vue',
  description: 'Get element plus vue',
  execute: async ({ context }) => {
    return `  <el-form ref="ruleFormRef" style="max-width: 600px" :model="ruleForm" :rules="rules" label-width="auto">
    <el-form-item label="Activity name" prop="name">
      <el-input v-model="ruleForm.name" />
    </el-form-item>
    <el-form-item label="Activity zone" prop="region">
      <el-select v-model="ruleForm.region" placeholder="Activity zone">
        <el-option label="Zone one" value="shanghai" />
        <el-option label="Zone two" value="beijing" />
      </el-select>
    </el-form-item>
    <el-form-item label="Activity count" prop="count">
      <el-select-v2 v-model="ruleForm.count" placeholder="Activity count" :options="options" />
    </el-form-item>
    <el-form-item label="Activity time" required>
      <el-col :span="11">
        <el-form-item prop="date1">
          <el-date-picker
            v-model="ruleForm.date1"
            type="date"
            aria-label="Pick a date"
            placeholder="Pick a date"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col class="text-center" :span="2">
        <span class="text-gray-500">-</span>
      </el-col>
      <el-col :span="11">
        <el-form-item prop="date2">
          <el-time-picker v-model="ruleForm.date2" aria-label="Pick a time" placeholder="Pick a time" style="width: 100%" />
        </el-form-item>
      </el-col>
    </el-form-item>
    <el-form-item label="Instant delivery" prop="delivery">
      <el-switch v-model="ruleForm.delivery" />
    </el-form-item>
    <el-form-item label="Activity location" prop="location">
      <el-segmented v-model="ruleForm.location" :options="locationOptions" />
    </el-form-item>
    <el-form-item label="Activity type" prop="type">
      <el-checkbox-group v-model="ruleForm.type">
        <el-checkbox value="Online activities" name="type"> Online activities </el-checkbox>
        <el-checkbox value="Promotion activities" name="type"> Promotion activities </el-checkbox>
        <el-checkbox value="Offline activities" name="type"> Offline activities </el-checkbox>
        <el-checkbox value="Simple brand exposure" name="type"> Simple brand exposure </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="Resources" prop="resource">
      <el-radio-group v-model="ruleForm.resource">
        <el-radio value="Sponsorship">Sponsorship</el-radio>
        <el-radio value="Venue">Venue</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="Activity form" prop="desc">
      <el-input v-model="ruleForm.desc" type="textarea" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm(ruleFormRef)"> Create </el-button>
      <el-button @click="resetForm(ruleFormRef)">Reset</el-button>
    </el-form-item>
  </el-form>`
  }

});

// 执行 JavaScript 代码工具
export const executeJavaScript = createTool({
  id: 'execute-javascript',
  description: 'Execute custom JavaScript code in the browser context',
  inputSchema: z.object({
    code: z.string().describe('JavaScript code to execute in the browser context'),
  }),
  execute: async ({ context }) => {
    const { code } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      const result = await page.evaluate((code) => {
        return eval(code);
      }, code);

      return {
        success: true,
        result,
        message: 'Successfully executed JavaScript code',
      };
    } catch (error) {
      return {
        success: false,
        result: null,
        message: `Failed to execute JavaScript: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

// 断开连接工具
export const disconnectBrowser = createTool({
  id: 'disconnect-browser',
  description: 'Disconnect from the current browser',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      await manager.disconnect();
      return {
        success: true,
        message: 'Successfully disconnected from browser',
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to disconnect: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

// 等待元素工具
export const waitForElement = createTool({
  id: 'wait-for-element',
  description: 'Wait for an element to appear on the page',
  inputSchema: z.object({
    selector: z.string().describe('CSS selector of the element to wait for'),
    timeout: z.number().optional().default(30000).describe('Timeout in milliseconds'),
  }),
  execute: async ({ context }) => {
    const { selector, timeout } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      await page.waitForSelector(selector, { timeout });

      return {
        success: true,
        message: `Element ${selector} appeared on the page`,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to wait for element: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

// 点击元素工具
export const clickElement = createTool({
  id: 'click-element',
  description: 'Click on an element in the page',
  inputSchema: z.object({
    selector: z.string().describe('CSS selector of the element to click'),
  }),
  execute: async ({ context }) => {
    const { selector } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      await page.click(selector);

      return {
        success: true,
        message: `Successfully clicked element ${selector}`,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to click element: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

// 输入文本工具
export const typeText = createTool({
  id: 'type-text',
  description: 'Type text into an input element',
  inputSchema: z.object({
    selector: z.string().describe('CSS selector of the input element'),
    text: z.string().describe('Text to type'),
    clear: z.boolean().optional().default(false).describe('Clear the input before typing'),
  }),
  execute: async ({ context }) => {
    const { selector, text, clear } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      if (clear) {
        await page.fill(selector, text);
      } else {
        await page.type(selector, text);
      }

      return {
        success: true,
        message: `Successfully typed text into ${selector}`,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to type text: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

// 截图工具
export const takeScreenshot = createTool({
  id: 'take-screenshot',
  description: 'Take a screenshot of the current page or specific element',
  inputSchema: z.object({
    selector: z.string().optional().describe('CSS selector to screenshot specific element'),
    path: z.string().optional().describe('File path to save screenshot'),
    fullPage: z.boolean().optional().default(false).describe('Take full page screenshot'),
  }),
  execute: async ({ context }) => {
    const { selector, path, fullPage } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      let screenshotOptions: any = { fullPage };
      if (path) {
        screenshotOptions.path = path;
      }

      let screenshot;
      if (selector) {
        const element = await page.$(selector);
        if (element) {
          screenshot = await element.screenshot(screenshotOptions);
        } else {
          throw new Error(`Element ${selector} not found`);
        }
      } else {
        screenshot = await page.screenshot(screenshotOptions);
      }

      return {
        success: true,
        message: 'Screenshot taken successfully',
        screenshot: screenshot.toString('base64'),
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to take screenshot: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
}); 