# Playwright Tools 使用指南

这个工具集允许你通过 WebSocket endpoints 连接到浏览器并执行各种操作，包括 window 全局方法。

## 功能特性

1. **通过 WebSocket 连接浏览器** - 支持连接到远程或本地浏览器实例
2. **执行 window 全局方法** - 如 `alert()`, `confirm()`, `prompt()` 等
3. **页面导航** - 导航到指定 URL
4. **获取页面信息** - 获取页面标题、URL、元素信息等
5. **执行自定义 JavaScript** - 在浏览器上下文中执行任意 JavaScript 代码
6. **连接管理** - 连接和断开浏览器连接

## 快速开始

### 1. 启动浏览器

首先，你需要一个支持远程调试的浏览器实例：

```bash
# 启动 Playwright 管理的浏览器
npm run browser start

# 或者启动系统浏览器（Chrome）
npm run browser system

# 查看运行中的浏览器
npm run browser list
```

### 2. 获取 WebSocket Endpoint

启动浏览器后，你会看到类似这样的输出：
```
✅ Browser started successfully!
📡 WebSocket Endpoint: ws://127.0.0.1:9222/devtools/browser/abc123...
🌐 Remote debugging port: 9222
```

复制这个 WebSocket endpoint 用于连接。

### 3. 使用工具

```typescript
import { 
  connectBrowserTool,
  executeWindowMethodTool,
  navigateTool,
  getPageInfoTool,
  executeJavaScriptTool,
  disconnectBrowserTool 
} from './src/mastra/tools/playwright-tool';

// 1. 连接到浏览器
const connectResult = await connectBrowserTool.execute({
  wsEndpoint: 'ws://127.0.0.1:9222/devtools/browser/your-endpoint-here'
});

// 2. 导航到页面
await navigateTool.execute({
  url: 'https://example.com'
});

// 3. 执行 window 方法
await executeWindowMethodTool.execute({
  method: 'alert',
  args: ['Hello World!']
});

// 4. 执行自定义 JavaScript
await executeJavaScriptTool.execute({
  code: 'return document.title;'
});
```

## 工具详细说明

### connectBrowserTool

连接到指定的浏览器实例。

**参数：**
- `wsEndpoint` (string): WebSocket endpoint URL

**返回：**
- `success` (boolean): 连接是否成功
- `message` (string): 连接结果消息

### executeWindowMethodTool

执行 window 对象上的全局方法。

**参数：**
- `method` (string): 要执行的方法名 (如 'alert', 'confirm', 'prompt')
- `args` (array, 可选): 传递给方法的参数

**返回：**
- `success` (boolean): 执行是否成功
- `result` (any): 方法返回值
- `message` (string): 执行结果消息

**支持的常见 window 方法：**
- `alert(message)` - 显示警告对话框
- `confirm(message)` - 显示确认对话框
- `prompt(message, defaultText)` - 显示输入对话框
- `open(url, name, features)` - 打开新窗口
- `close()` - 关闭窗口
- `focus()` - 聚焦窗口
- `blur()` - 失焦窗口

### navigateTool

导航到指定的 URL。

**参数：**
- `url` (string): 要导航到的 URL

**返回：**
- `success` (boolean): 导航是否成功
- `message` (string): 导航结果消息
- `url` (string): 当前页面 URL

### getPageInfoTool

获取当前页面信息。

**参数：**
- `selector` (string, 可选): CSS 选择器，获取特定元素信息

**返回：**
- `success` (boolean): 获取是否成功
- `data` (object): 页面数据，包含 URL、标题等
- `message` (string): 操作结果消息

### executeJavaScriptTool

在浏览器上下文中执行自定义 JavaScript 代码。

**参数：**
- `code` (string): 要执行的 JavaScript 代码

**返回：**
- `success` (boolean): 执行是否成功
- `result` (any): 代码执行结果
- `message` (string): 执行结果消息

### disconnectBrowserTool

断开与浏览器的连接。

**参数：** 无

**返回：**
- `success` (boolean): 断开是否成功
- `message` (string): 操作结果消息

## 使用示例

### 基本操作示例

```typescript
// 执行基本的 window 方法
await executeWindowMethodTool.execute({
  method: 'alert',
  args: ['这是一个警告消息']
});

// 获取用户确认
const confirmResult = await executeWindowMethodTool.execute({
  method: 'confirm',
  args: ['你确定要继续吗？']
});
console.log('用户选择:', confirmResult.result); // true 或 false

// 获取用户输入
const promptResult = await executeWindowMethodTool.execute({
  method: 'prompt',
  args: ['请输入你的名字:', '默认名字']
});
console.log('用户输入:', promptResult.result);
```

### 高级 JavaScript 操作

```typescript
// 操作页面元素
await executeJavaScriptTool.execute({
  code: `
    // 创建新元素
    const div = document.createElement('div');
    div.textContent = '这是通过 Playwright 工具创建的元素';
    div.style.cssText = 'position: fixed; top: 0; left: 0; background: red; color: white; padding: 10px; z-index: 9999;';
    document.body.appendChild(div);
    
    return '元素已创建';
  `
});

// 获取页面数据
const pageData = await executeJavaScriptTool.execute({
  code: `
    return {
      title: document.title,
      url: window.location.href,
      userAgent: navigator.userAgent,
      cookies: document.cookie,
      localStorage: Object.keys(localStorage),
      sessionStorage: Object.keys(sessionStorage),
      windowSize: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
  `
});
```

### 操作本地存储

```typescript
// 设置和获取 localStorage
await executeJavaScriptTool.execute({
  code: `
    localStorage.setItem('myKey', 'myValue');
    localStorage.setItem('config', JSON.stringify({theme: 'dark', lang: 'zh'}));
  `
});

// 读取 localStorage
const storageData = await executeJavaScriptTool.execute({
  code: `
    return {
      myKey: localStorage.getItem('myKey'),
      config: JSON.parse(localStorage.getItem('config') || '{}'),
      allKeys: Object.keys(localStorage)
    };
  `
});
```

## 故障排除

### 常见问题

1. **连接失败**
   - 确保浏览器正在运行并启用了远程调试
   - 检查 WebSocket endpoint 是否正确
   - 确认端口没有被防火墙阻止

2. **window 方法执行失败**
   - 某些方法可能被浏览器阻止（如 `alert` 在无头模式下）
   - 检查方法名拼写是否正确
   - 确认方法在当前上下文中可用

3. **JavaScript 执行错误**
   - 检查 JavaScript 语法
   - 确保代码在浏览器环境中有效
   - 使用 `try-catch` 包装复杂代码

### 调试技巧

1. **查看浏览器控制台**
   ```typescript
   await executeJavaScriptTool.execute({
     code: 'console.log("调试消息"); return "完成";'
   });
   ```

2. **检查页面状态**
   ```typescript
   const pageState = await getPageInfoTool.execute({});
   console.log('页面状态:', pageState);
   ```

3. **测试连接**
   ```typescript
   const testResult = await executeJavaScriptTool.execute({
     code: 'return "连接正常";'
   });
   ```

## 进阶用法

### 自定义工具封装

你可以基于这些基础工具创建更高级的功能：

```typescript
// 自定义页面截图工具
async function takeScreenshot() {
  return await executeJavaScriptTool.execute({
    code: `
      // 使用 html2canvas 或其他库截图
      // 这里是示例代码
      return '截图已保存';
    `
  });
}

// 自定义表单填写工具
async function fillForm(formData: Record<string, string>) {
  const code = `
    ${Object.entries(formData).map(([name, value]) => 
      `document.querySelector('[name="${name}"]').value = "${value}";`
    ).join('\n')}
    
    return '表单已填写';
  `;
  
  return await executeJavaScriptTool.execute({ code });
}
```

### 批量操作

```typescript
// 批量执行多个 window 方法
const operations = [
  { method: 'alert', args: ['第一个消息'] },
  { method: 'confirm', args: ['确认操作？'] },
  { method: 'prompt', args: ['输入内容:', ''] }
];

for (const op of operations) {
  const result = await executeWindowMethodTool.execute(op);
  console.log(`${op.method} 结果:`, result);
}
```

## 最佳实践

1. **连接管理**
   - 使用完毕后总是断开连接
   - 在应用退出时清理资源

2. **错误处理**
   - 始终检查操作结果的 `success` 字段
   - 为网络错误和超时设置重试机制

3. **安全考虑**
   - 不要在生产环境中暴露远程调试端口
   - 验证和清理用户输入的 JavaScript 代码

4. **性能优化**
   - 复用浏览器连接
   - 避免频繁的连接/断开操作
   - 批量执行相关操作 