config:
  name: "充电桩管理测试套件"
  description: "充电桩管理页面的新增、删除、查询功能测试"
  baseUrl: "http://************:9999"
  executorType: "element-plus"
  testMode: "flow"
  timeout: 30000

# 统一表单数据配置变量
variables:
  testData:
    # 新增充电桩测试数据
    addPileData:
      pileName: "自动化测试充电桩"
      pileCode: "AUTO_TEST_PILE_NEW"
      modelNumber: "测试型号X2"
      interfaceCodeA: "INTERFACE_A_001"
      interfaceCodeB: "INTERFACE_B_001"
    
    # 删除充电桩测试数据
    deletePileData:
      pileName: "自动化测试充电桩"
    
    # 查询测试数据
    searchData:
      searchText: "CDZ"
    
    # 编辑充电桩测试数据
    editPileData:
      newPileName: "CDZ-02编辑测试"
      newModelNumber: "C1编辑"
      newInterfaceCodeA: "EDIT_A_001"
      newInterfaceCodeB: "EDIT_B_001"

templates:
  add-charge-pile-template:
    name: "新增充电桩模板"
    type: "shared"
    parameters:
      - name: pileName
        type: "string"
        required: true
      - name: pileCode
        type: "string"
        required: true
      - name: modelNumber
        type: "string"
        required: false
      - name: interfaceCodeA
        type: "string"
        required: false
      - name: interfaceCodeB
        type: "string"
        required: false
    steps:
      - action: click
        role: button
        roleOptions:
          name: "新增充电桩"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩名称"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{pileName}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{pileCode}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩型号"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{modelNumber}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "1#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{interfaceCodeA}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "2#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{interfaceCodeB}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 2000

  delete-charge-pile-template:
    name: "删除充电桩模板"
    type: "shared"
    parameters:
      - name: pileName
        type: "string"
        required: true
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "deleteRow"
          fields:
            - column: "名称"
              value: "{{pileName}}"
              exact: true
          timeout: 1000

  search-charge-pile-template:
    name: "查询充电桩模板"
    type: "shared"
    parameters:
      - name: searchText
        type: "string"
        required: false
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩名称/编码"
        data: "{{searchText}}"
      - action: click
        role: button
        roleOptions:
          name: "查询"

  edit-charge-pile-template:
    name: "编辑充电桩模板"
    type: "shared"
    parameters:
      - name: targetPileName
        type: "string"
        required: true
      - name: newPileName
        type: "string"
        required: false
      - name: newModelNumber
        type: "string"
        required: false
      - name: newInterfaceCodeA
        type: "string"
        required: false
      - name: newInterfaceCodeB
        type: "string"
        required: false
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "editRow"
          fields:
            - column: "名称"
              value: "{{targetPileName}}"
              exact: true
          timeout: 5000
      - action: wait
        data: 1000
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩名称"
        within:
          role: dialog
          roleOptions:
            name: "修改充电桩"
        data: "{{newPileName}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩型号"
        within:
          role: dialog
          roleOptions:
            name: "修改充电桩"
        data: "{{newModelNumber}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "1#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "修改充电桩"
        data: "{{newInterfaceCodeA}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "2#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "修改充电桩"
        data: "{{newInterfaceCodeB}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 2000

tests:
  - name: "充电桩查询功能测试"
    description: "测试充电桩的查询功能"
    steps:
      - action: navigate
        url: "/#/deviceManage/chargeManage/index"
      - action: useTemplate
        template: "search-charge-pile-template"
        parameters:
          searchText: "{{testData.searchData.searchText}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "CDZ-02"
        assertion: visible

  - name: "充电桩新增功能测试"
    description: "测试充电桩的新增功能"
    steps:
      - action: navigate
        url: "/#/deviceManage/chargeManage/index"
      - action: useTemplate
        template: "add-charge-pile-template"
        parameters:
          pileName: "{{testData.addPileData.pileName}}"
          pileCode: "{{testData.addPileData.pileCode}}"
          modelNumber: "{{testData.addPileData.modelNumber}}"
          interfaceCodeA: "{{testData.addPileData.interfaceCodeA}}"
          interfaceCodeB: "{{testData.addPileData.interfaceCodeB}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.addPileData.pileName}}"
        assertion: visible

  - name: "充电桩删除功能测试"
    description: "测试充电桩的删除功能"
    steps:
      - action: useTemplate
        template: "delete-charge-pile-template"
        parameters:
          pileName: "{{testData.deletePileData.pileName}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.deletePileData.pileName}}"
        assertion: hidden

  - name: "充电桩编辑功能测试"
    description: "测试充电桩的编辑功能"
    steps:
      - action: navigate
        url: "/#/deviceManage/chargeManage/index"
      - action: useTemplate
        template: "edit-charge-pile-template"
        parameters:
          targetPileName: "CDZ-02"
          newPileName: "{{testData.editPileData.newPileName}}"
          newModelNumber: "{{testData.editPileData.newModelNumber}}"
          newInterfaceCodeA: "{{testData.editPileData.newInterfaceCodeA}}"
          newInterfaceCodeB: "{{testData.editPileData.newInterfaceCodeB}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.editPileData.newPileName}}"
        assertion: visible

  - name: "充电桩完整流程测试"
    description: "测试充电桩的完整新增、编辑和删除流程"
    steps:
      - action: navigate
        url: "/#/deviceManage/chargeManage/index"
      - action: useTemplate
        template: "add-charge-pile-template"
        parameters:
          pileName: "{{testData.addPileData.pileName}}"
          pileCode: "{{testData.addPileData.pileCode}}"
          modelNumber: "{{testData.addPileData.modelNumber}}"
          interfaceCodeA: "{{testData.addPileData.interfaceCodeA}}"
          interfaceCodeB: "{{testData.addPileData.interfaceCodeB}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.addPileData.pileName}}"
        assertion: visible
      - action: wait
        data: 1000
      - action: useTemplate
        template: "search-charge-pile-template"
        parameters:
          searchText: "{{testData.addPileData.pileName}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.addPileData.pileName}}"
        assertion: visible
      - action: wait
        data: 1000
      - action: useTemplate
        template: "delete-charge-pile-template"
        parameters:
          pileName: "{{testData.deletePileData.pileName}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.deletePileData.pileName}}"
        assertion: hidden