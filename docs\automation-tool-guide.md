# Automation Testing Tools Guide

This guide provides comprehensive documentation for the enhanced automation testing tools built with <PERSON><PERSON> and <PERSON><PERSON>.

## Overview

The automation testing toolkit provides a complete solution for browser automation testing using YAML-based test scripts. It includes tools for browser connection management, page analysis, test execution, and result monitoring.

## Architecture

### Core Components

1. **PlaywrightManager**: Manages browser connections via WebSocket endpoints
2. **Automation Tools**: Individual tools for specific automation tasks
3. **Automation Agent**: AI agent that orchestrates all automation capabilities
4. **PageAutomationAPI**: Browser-side API for test execution

### Browser Connection Flow

```mermaid
graph TD
    A[Start Browser with Remote Debugging] --> B[Get WebSocket Endpoint]
    B --> C[Connect via connectPage Tool]
    C --> D[Browser Ready for Automation]
    D --> E[Execute Automation Tasks]
    E --> F[Disconnect via disconnectBrowser]
```

## Available Tools

### 1. Browser Connection Management

#### `connectPage`
**Purpose**: Establish connection to a browser instance via WebSocket endpoint.

**Parameters**:
- `wsEndpoint` (string): WebSocket URL for Chrome DevTools Protocol connection

**Example**:
```typescript
await connectPage.execute({
  context: {
    wsEndpoint: "ws://127.0.0.1:9222/devtools/browser/abc123"
  }
});
```

#### `disconnectBrowser`
**Purpose**: Clean up browser connection and resources.

**Parameters**: None

### 2. Page Analysis Tools

#### `getSnapshotForAI`
**Purpose**: Capture accessibility tree snapshot optimized for AI analysis.

**Returns**: Structured page representation with DOM elements and accessibility info.

#### `getPageVarsElements`
**Purpose**: Extract dynamic elements, form fields, and interactive components.

**Returns**: Page variable elements with their current states and values.

#### `getYamlSchema`
**Purpose**: Retrieve YAML schema definition for test script generation.

**Returns**: Complete schema specification for writing automation tests.

### 3. YAML Test Management

#### `validateYaml`
**Purpose**: Validate YAML test scripts against automation schema.

**Parameters**:
- `yamlContent` (string): YAML test script to validate

**Returns**: Validation result with errors, warnings, and suggestions.

#### `executeYamlTest`
**Purpose**: Execute YAML-based automation test scripts.

**Parameters**:
- `yamlContent` (string): YAML test script content
- `options` (object, optional): Execution configuration
  - `timeout` (number): Test timeout in milliseconds
  - `continueOnFailure` (boolean): Continue on step failures
  - `captureScreenshots` (boolean): Enable screenshot capture
  - `verbose` (boolean): Enable detailed logging

**Returns**: Test execution result with step details and timing.

### 4. Test Monitoring & Management

#### `getTestResult`
**Purpose**: Retrieve detailed results of completed tests.

**Parameters**:
- `testId` (string): Unique test execution identifier

#### `getTestProgress`
**Purpose**: Monitor real-time execution progress of running tests.

**Parameters**:
- `testId` (string): Test execution identifier

#### `cancelTest`
**Purpose**: Cancel currently running test execution.

**Parameters**:
- `testId` (string): Test execution identifier to cancel

#### `getRunningTests`
**Purpose**: List all currently active test executions.

**Returns**: Array of running test IDs and basic information.

#### `getTestHistory`
**Purpose**: Retrieve historical test execution records.

**Parameters**:
- `limit` (number, optional): Maximum records to return (default: 10)

#### `clearTestHistory`
**Purpose**: Permanently clear all test execution history.

**Parameters**:
- `confirm` (boolean): Must be true to proceed with clearing

## Usage Examples

### Basic Test Execution Workflow

```typescript
// 1. Connect to browser
await connectPage.execute({
  context: { wsEndpoint: "ws://127.0.0.1:9222/devtools/browser/abc123" }
});

// 2. Get page information for test creation
const snapshot = await getSnapshotForAI.execute({});
const schema = await getYamlSchema.execute({});

// 3. Validate test script
const validation = await validateYaml.execute({
  context: { yamlContent: testScript }
});

// 4. Execute test if valid
if (validation.result.isValid) {
  const execution = await executeYamlTest.execute({
    context: {
      yamlContent: testScript,
      options: {
        timeout: 30000,
        captureScreenshots: true,
        verbose: true
      }
    }
  });
  
  // 5. Monitor progress
  const progress = await getTestProgress.execute({
    context: { testId: execution.testId }
  });
}

// 6. Clean up
await disconnectBrowser.execute({});
```

### Using the Automation Agent

```typescript
import { automationAgent } from '../src/mastra/agents/automation-agent';

const response = await automationAgent.generate([
  {
    role: 'user',
    content: `Connect to browser at ws://localhost:9222/devtools/browser/abc123, 
              analyze the current page, and create a test script to verify the login form works correctly.`
  }
]);
```

## Error Handling

All tools include comprehensive error handling with:
- Detailed error messages
- Connection status validation
- Resource cleanup on failures
- Graceful degradation

## Best Practices

1. **Always connect before operations**: Use `connectPage` before any other automation tools
2. **Validate before execution**: Use `validateYaml` to catch issues early
3. **Monitor long-running tests**: Use `getTestProgress` for tests that may take time
4. **Clean up resources**: Use `disconnectBrowser` when finished
5. **Handle errors gracefully**: Check tool responses for success/failure status
6. **Use appropriate timeouts**: Configure realistic timeouts for your test scenarios

## Troubleshooting

### Common Issues

1. **Connection Failed**: Ensure browser is started with remote debugging enabled
2. **Page Not Found**: Verify WebSocket endpoint URL is correct and accessible
3. **Test Validation Errors**: Check YAML syntax and schema compliance
4. **Execution Timeouts**: Increase timeout values for complex operations

### Debug Tips

- Enable verbose logging in test execution options
- Use `getRunningTests` to check active executions
- Review test history for patterns in failures
- Capture screenshots to understand test context
