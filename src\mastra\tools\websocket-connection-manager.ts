import { chromium, type Browser, type Page, type BrowserContext } from '@playwright/test';
import { createTool } from '@mastra/core/tools';
import { z } from 'zod';

/**
 * WebSocket 连接配置选项
 */
export interface WebSocketConnectionOptions {
  /** WebSocket 调试器 URL */
  webSocketDebuggerUrl: string;
  /** 连接超时时间（毫秒） */
  connectionTimeout?: number;
  /** 是否等待页面加载完成 */
  waitForLoad?: boolean;
}

/**
 * WebSocket 连接结果
 */
export interface WebSocketConnectionResult {
  /** 浏览器实例 */
  browser: Browser;
  /** 页面实例 */
  page: Page;
  /** 浏览器上下文 */
  context: BrowserContext;
  /** 连接的 URL */
  url: string;
  /** 是否连接成功 */
  success: boolean;
}

/**
 * WebSocket 连接管理器
 * 负责通过 webSocketDebuggerUrl 连接到指定的浏览器页面
 */
export class WebSocketConnectionManager {
  private static readonly CONNECTION_TIMEOUT = 10000;
  private static connections: Map<string, WebSocketConnectionResult> = new Map();

  /**
   * 通过 WebSocket 调试器 URL 连接到浏览器页面
   */
  static async connectToPage(options: WebSocketConnectionOptions): Promise<WebSocketConnectionResult> {
    const {
      webSocketDebuggerUrl,
      connectionTimeout = this.CONNECTION_TIMEOUT,
      waitForLoad = true,
    } = options;

    console.log('开始连接到 WebSocket 调试器', { webSocketDebuggerUrl });

    try {
      // 检查是否已经有相同的连接
      const existingConnection = this.connections.get(webSocketDebuggerUrl);
      if (existingConnection && !existingConnection.page.isClosed()) {
        console.log('复用现有连接', { url: webSocketDebuggerUrl });
        return existingConnection;
      }

      // 从 WebSocket URL 提取 CDP 端点
      const cdpEndpoint = this.extractCDPEndpoint(webSocketDebuggerUrl);
      console.log('提取的 CDP 端点', { cdpEndpoint });

      // 连接到浏览器
      const browser = await chromium.connectOverCDP(cdpEndpoint);
      console.log('成功连接到浏览器');

      // 获取所有上下文和页面
      const contexts = browser.contexts();
      if (contexts.length === 0) {
        throw new Error('没有找到浏览器上下文');
      }

      const context = contexts[0];
      const pages = context.pages();
      
      if (pages.length === 0) {
        throw new Error('没有找到页面');
      }

      // 选择第一个页面或根据 URL 匹配页面
      const page = pages[0];
      console.log('选择页面', { url: page.url() });

      // 等待页面加载完成
      if (waitForLoad) {
        await page.waitForLoadState('domcontentloaded', { timeout: connectionTimeout });
        console.log('页面加载完成');
      }

      const result: WebSocketConnectionResult = {
        browser,
        page,
        context,
        url: page.url(),
        success: true,
      };

      // 缓存连接
      this.connections.set(webSocketDebuggerUrl, result);
      console.log('WebSocket 连接建立成功');

      return result;
    } catch (error) {
      console.error('WebSocket 连接失败', {
        webSocketDebuggerUrl,
        error: error instanceof Error ? error.message : String(error),
      });

      throw new Error(`WebSocket 连接失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 从 WebSocket 调试器 URL 提取 CDP 端点
   */
  private static extractCDPEndpoint(webSocketDebuggerUrl: string): string {
    try {
      const url = new URL(webSocketDebuggerUrl);
      
      // WebSocket URL 格式通常是: ws://host:port/devtools/page/{pageId}
      // 对应的 CDP 端点是: http://host:port
      const cdpEndpoint = `http://${url.host}`;
      
      return cdpEndpoint;
    } catch (error) {
      throw new Error(`无效的 WebSocket 调试器 URL: ${webSocketDebuggerUrl}`);
    }
  }

  /**
   * 断开指定的 WebSocket 连接
   */
  static async disconnect(webSocketDebuggerUrl: string): Promise<void> {
    const connection = this.connections.get(webSocketDebuggerUrl);
    if (connection) {
      try {
        if (!connection.page.isClosed()) {
          await connection.page.close();
        }
        if (!connection.browser.isConnected()) {
          await connection.browser.close();
        }
        this.connections.delete(webSocketDebuggerUrl);
        console.log('WebSocket 连接已断开', { webSocketDebuggerUrl });
      } catch (error) {
        console.error('断开 WebSocket 连接时出错', { error });
      }
    }
  }

  /**
   * 断开所有 WebSocket 连接
   */
  static async disconnectAll(): Promise<void> {
    const urls = Array.from(this.connections.keys());
    await Promise.all(urls.map(url => this.disconnect(url)));
    console.log('所有 WebSocket 连接已断开');
  }

  /**
   * 获取当前活跃的连接列表
   */
  static getActiveConnections(): string[] {
    return Array.from(this.connections.keys()).filter(url => {
      const connection = this.connections.get(url);
      return connection && !connection.page.isClosed();
    });
  }
}

/**
 * 连接到 WebSocket 调试器的工具
 */
export const connectToWebSocketDebugger = createTool({
  id: 'connect-to-websocket-debugger',
  description: '通过 WebSocket 调试器 URL 连接到指定的浏览器页面',
  inputSchema: z.object({
    webSocketDebuggerUrl: z.string().describe('WebSocket 调试器 URL，格式如: ws://localhost:9222/devtools/page/{pageId}'),
    waitForLoad: z.boolean().optional().default(true).describe('是否等待页面加载完成'),
  }),
  execute: async ({ context }) => {
    const { webSocketDebuggerUrl, waitForLoad } = context;
    
    try {
      const result = await WebSocketConnectionManager.connectToPage({
        webSocketDebuggerUrl,
        waitForLoad,
      });

      return {
        success: true,
        message: `成功连接到页面: ${result.url}`,
        pageUrl: result.url,
        connectionId: webSocketDebuggerUrl,
      };
    } catch (error) {
      return {
        success: false,
        message: `连接失败: ${error instanceof Error ? error.message : String(error)}`,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

/**
 * 断开 WebSocket 连接的工具
 */
export const disconnectWebSocketDebugger = createTool({
  id: 'disconnect-websocket-debugger',
  description: '断开指定的 WebSocket 调试器连接',
  inputSchema: z.object({
    webSocketDebuggerUrl: z.string().describe('要断开的 WebSocket 调试器 URL'),
  }),
  execute: async ({ context }) => {
    const { webSocketDebuggerUrl } = context;
    
    try {
      await WebSocketConnectionManager.disconnect(webSocketDebuggerUrl);
      return {
        success: true,
        message: `成功断开连接: ${webSocketDebuggerUrl}`,
      };
    } catch (error) {
      return {
        success: false,
        message: `断开连接失败: ${error instanceof Error ? error.message : String(error)}`,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

/**
 * 获取活跃连接列表的工具
 */
export const getActiveWebSocketConnections = createTool({
  id: 'get-active-websocket-connections',
  description: '获取当前活跃的 WebSocket 调试器连接列表',
  inputSchema: z.object({}),
  execute: async () => {
    const activeConnections = WebSocketConnectionManager.getActiveConnections();
    return {
      success: true,
      connections: activeConnections,
      count: activeConnections.length,
    };
  },
});
