config:
  name: "驾驶舱管理完整测试"
  description: "驾驶舱管理的新增、修改、删除、查询、重置功能完整测试"
  executorType: "element-plus"
  testMode: "flow"
  timeout: 30000
  variables:
    cockpit:
      name: "自动化测试驾驶舱"
      machineCode: "AUTO123456789TEST"
    editCockpit:
      name: "修改后的驾驶舱名称"
      machineCode: "MODIFIED123456789ABC"

templates:
  cockpit-add-template:
    name: "新增驾驶舱模板"
    type: "shared"
    parameters:
      - name: cockpitName
        type: "string"
        required: true
      - name: machineCode
        type: "string"
        required: true
    steps:
      - action: click
        role: button
        roleOptions:
          name: "新增驾驶舱"
      - action: fill
        role: textbox
        roleOptions:
          name: "驾驶舱名称"
        within:
          role: dialog
          roleOptions:
            name: "新增驾驶舱"
        data: "{{cockpitName}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "机器码"
        within:
          role: dialog
          roleOptions:
            name: "新增驾驶舱"
        data: "{{machineCode}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"

  cockpit-edit-template:
    name: "修改驾驶舱模板"
    type: "shared"
    parameters:
      - name: cockpitName
        type: "string"
        required: true
      - name: machineCode
        type: "string"
        required: true
    steps:
      - action: click
        role: button
        roleOptions:
          name: "操作"
      - action: wait
        data: 500
      - action: click
        role: button
        roleOptions:
          name: "修改"
      - action: fill
        role: textbox
        roleOptions:
          name: "驾驶舱名称"
        within:
          role: dialog
          roleOptions:
            name: "修改驾驶舱"
        data: "{{cockpitName}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "机器码"
        within:
          role: dialog
          roleOptions:
            name: "修改驾驶舱"
        data: "{{machineCode}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"

  cockpit-delete-template:
    name: "删除驾驶舱模板"
    type: "shared"
    parameters:
      - name: cockpitName
        type: "string"
        required: true
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "deleteRow"
          fields:
            - column: "名称"
              value: "{{cockpitName}}"
              exact: true

  cockpit-search-template:
    name: "查询驾驶舱模板"
    type: "shared"
    parameters:
      - name: searchName
        type: "string"
        required: false
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "驾驶舱名称"
        data: "{{searchName}}"
      - action: click
        role: button
        roleOptions:
          name: "查询"

  cockpit-reset-template:
    name: "重置查询模板"
    type: "shared"
    parameters: []
    steps:
      - action: click
        role: button
        roleOptions:
          name: "重置"

tests:
  - name: "驾驶舱新增测试"
    description: "测试新增驾驶舱功能"
    steps:
      - action: useTemplate
        template: "cockpit-add-template"
        parameters:
          cockpitName: "{{cockpit.name}}"
          machineCode: "{{cockpit.machineCode}}"

  - name: "驾驶舱查询测试"
    description: "测试查询驾驶舱功能"
    steps:
      - action: useTemplate
        template: "cockpit-search-template"
        parameters:
          searchName: "{{cockpit.name}}"

  - name: "驾驶舱修改测试"
    description: "测试修改驾驶舱功能"
    steps:
      - action: useTemplate
        template: "cockpit-edit-template"
        parameters:
          cockpitName: "{{editCockpit.name}}"
          machineCode: "{{editCockpit.machineCode}}"

  - name: "驾驶舱查询修改后数据测试"
    description: "验证修改后的数据"
    steps:
      - action: useTemplate
        template: "cockpit-search-template"
        parameters:
          searchName: "{{editCockpit.name}}"

  - name: "驾驶舱重置查询测试"
    description: "测试重置查询功能"
    steps:
      - action: useTemplate
        template: "cockpit-reset-template"

  - name: "驾驶舱删除测试"
    description: "测试删除驾驶舱功能"
    steps:
      - action: useTemplate
        template: "cockpit-delete-template"
        parameters:
          cockpitName: "{{editCockpit.name}}"