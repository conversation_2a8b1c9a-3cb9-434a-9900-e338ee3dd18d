templates:
  add-vehicle-template:
    name: "新增车辆模板"
    type: "shared"
    parameters:
      - name: carName
        type: "string"
        required: true
      - name: carCode
        type: "string"
        required: true
      - name: modelNumber
        type: "string"
        required: false
      - name: plateNo
        type: "string"
        required: false
      - name: machineA
        type: "string"
        required: false
      - name: machineB
        type: "string"
        required: false
      - name: containerType
        type: "string"
        required: false
      - name: trashCanCount
        type: "string"
        required: false
      - name: waringPercent
        type: "string"
        required: false
      - name: runArea
        type: "string"
        required: false
    steps:
      - action: click
        role: button
        roleOptions:
          name: "添加"
        description: "点击添加按钮打开新增对话框"
      
      - action: wait
        data: 2000
        description: "等待对话框加载"
      
      - action: fill
        role: textbox
        roleOptions:
          name: "* 名称"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{carName}}"
        description: "填写车辆名称"
      
      - action: fill
        role: textbox
        roleOptions:
          name: "* 编码"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{carCode}}"
        description: "填写车辆编码"
      
      - action: selectOption
        role: combobox
        roleOptions:
          name: "型号"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{modelNumber}}"
        description: "选择车辆型号"
      
      - action: fill
        role: textbox
        roleOptions:
          name: "车牌号"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{plateNo}}"
        description: "填写车牌号"
      
      - action: selectOption
        role: combobox
        roleOptions:
          name: "机器A"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{machineA}}"
        description: "选择机器A"
      
      - action: selectOption
        role: combobox
        roleOptions:
          name: "机器B"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{machineB}}"
        description: "选择机器B"
      
      - action: selectOption
        role: combobox
        roleOptions:
          name: "预存柜"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{containerType}}"
        description: "选择预存柜类型"
      
      - action: fill
        role: textbox
        roleOptions:
          name: "请输入容量"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{trashCanCount}}"
        description: "填写废弃桶容量"
      
      - action: fill
        role: textbox
        roleOptions:
          name: "请输入预警值"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{waringPercent}}"
        description: "填写废弃桶预警值"
      
      - action: selectOption
        role: combobox
        roleOptions:
          name: "投放区域"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{runArea}}"
        description: "选择投放区域"
      
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 2000
        returnAs: "confirmResult"
        description: "确认提交表单"