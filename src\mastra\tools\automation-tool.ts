import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { chromium, <PERSON><PERSON><PERSON>, Browser<PERSON>ontext, Page } from 'playwright';
import path from 'path';
import fs from 'fs';


/**
 * Global browser manager for Playwright automation testing
 * Manages browser connections, contexts, and pages for automation tools
 */
class PlaywrightManager {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;

  /**
   * Connect to a browser instance via HTTP endpoint
   * @param wsEndpoint HTTP endpoint URL for Chrome DevTools Protocol (e.g., http://localhost:9222)
   */
 async connectToBrowser(wsEndpoint: string): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }

    this.browser = await chromium.connectOverCDP(wsEndpoint);
    this.context = this.browser.contexts()[0] || await this.browser.newContext();
    this.page = this.context.pages()[0] || await this.context.newPage();
  }

  /**
   * Ensure a browser connection is established
   * @throws Error if no browser connection is available
   */
  async ensureConnected(): Promise<void> {
    if (!this.page || !this.browser || !this.context) {
      throw new Error('No browser connection available. Please connect to a browser first using the connectPage tool.');
    }

    // Verify the browser is still connected
    if (!this.browser.isConnected()) {
      throw new Error('Browser connection lost. Please reconnect using the connectPage tool.');
    }
  }

  /**
   * Get the current page instance
   * @returns The current Playwright page
   * @throws Error if no page is available
   */
  getPage(): Page {
    if (!this.page) {
      throw new Error('No browser page available. Please connect to a browser first using the connectPage tool.');
    }
    return this.page;
  }

  /**
   * Disconnect from the browser and clean up resources
   */
  async disconnect(): Promise<void> {
    await this.cleanup();
  }

  /**
   * Internal cleanup method
   */
  private async cleanup(): Promise<void> {
    try {
      if (this.browser) {
        await this.browser.close();
      }
    } catch (error) {
      // Ignore cleanup errors
    } finally {
      this.browser = null;
      this.context = null;
      this.page = null;
    }
  }

  /**
   * Check if currently connected to a browser
   */
  isConnected(): boolean {
    return !!(this.browser && this.browser.isConnected() && this.page);
  }
}

const manager = new PlaywrightManager();

export const connectBrowser = createTool({
  id: 'connect-browser',
  description: 'Establish a connection to a running browser instance via Chrome DevTools Protocol (CDP) using an HTTP endpoint. This is the first step required before using any other automation tools. The browser must be started with remote debugging enabled (e.g., --remote-debugging-port=9222).',
  inputSchema: z.object({
    wsEndpoint: z.string().describe('HTTP endpoint URL for the Chrome DevTools Protocol connection. Typically http://localhost:9222 for local browser instances. The browser must be running with remote debugging enabled.'),
  }),
  execute: async ({ context }) => {
    const { wsEndpoint } = context;
    console.log('wsEndpoint', wsEndpoint, context);
    try {
        await manager.connectToBrowser(wsEndpoint);
      return {
        success: true,
        message: `Successfully connected to browser at ${wsEndpoint}`,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to connect to browser: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

// 导航工具
export const navigateToUrl = createTool({
  id: 'navigate-to-url',
  description: 'Navigate the connected browser to a specific URL. Supports HTTP, HTTPS, and file:// protocols. The navigation waits for the page to load completely before returning. Requires an active browser connection.',
  inputSchema: z.object({
    url: z.string().describe('Target URL to navigate to. Must include protocol (http://, https://, or file://). Examples: "https://example.com", "http://localhost:3000", "file:///path/to/file.html"'),
  }),
  execute: async ({ context }) => {
    const { url } = context;
    try {
      await manager.ensureConnected();
      const page = manager.getPage();

      await page.goto(url);
      const currentUrl = page.url();

      return {
        success: true,
        message: `Successfully navigated to ${url}`,
        url: currentUrl,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to navigate: ${error instanceof Error ? error.message : String(error)}`,
        url: '',
      };
    }
  },
});

export const getYamlSchema = createTool({
  id: 'get-yaml-schema',
  description: 'Retrieve the YAML schema definition for automation testing. This schema provides the structure and format specification for writing YAML-based test scripts, including available actions, selectors, assertions, and configuration options. Essential for LLM-generated test script creation.',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      await manager.ensureConnected();
      const page = manager.getPage();
      const yamlSchema = await page.evaluate(() => window.automationTesting.getYAMLSchema());
      return {
        success: true,
        message: 'Successfully retrieved YAML schema definition for test script generation',
        yamlSchema,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get YAML schema: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});


export const getSnapshotForAI = createTool({
  id: 'get-snapshot-for-ai',
  description: 'Capture an accessibility tree snapshot of the current page optimized for AI analysis. This snapshot provides a structured representation of the page\'s DOM elements, their properties, and accessibility information, enabling AI agents to understand the page structure and generate appropriate test actions.',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      await manager.ensureConnected();
      const page = manager.getPage();
      const snapshot = await page.evaluate(() => window.automationTesting.getSnapshotForAI());
      return {
        success: true,
        message: 'Successfully captured page accessibility snapshot for AI analysis',
        snapshot,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get page snapshot: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const getSourceCodeFile = createTool({
  id: 'get-source-code-file',
  description: 'Retrieve the source code of the currently loaded Vue.js component file in the browser. This tool automatically determines the file path based on the current route hash and fetches the corresponding .vue file from the project directory. Useful for analyzing component structure and understanding the code context.',
  // inputSchema: z.object({
  //   wholeModule: z.boolean().describe('Whether to retrieve the complete module source code including all dependencies and imports (true) or just the main component file (false)'),
  // }),
  execute: async ({ context }) => {
    const { wholeModule = true } = context;
    const page = manager.getPage();
    const sourceCode = await page.evaluate((wholeModule) => {
      const rootDir = "D:\\works\\coffee-ui\\src\\views\\"
      const hashPath = location.hash.startsWith('#/') ? location.hash.slice(2) : location.hash
      const hashArr = hashPath.split('/')
      const filePath = rootDir + hashArr.join('\\') + '.vue'
      return window.automationTesting.getSourceCodeFile(filePath, wholeModule)
    }, wholeModule);
    return {
      success: true,
      message: 'Successfully got source code file',
      sourceCode,
    };
  },
});

export const getPageVarsElements = createTool({
  id: 'get-page-vars-elements',
  description: 'Extract page variable elements and their current values from the DOM. This tool identifies dynamic elements, form fields, interactive components, and their states, providing context for test script generation and validation. Useful for understanding page state and available interaction targets.',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      await manager.ensureConnected();
      const page = manager.getPage();
      const pageVarsElements = await page.evaluate(() => window.automationTesting.getPageVarsElements());
      return {
        success: true,
        message: 'Successfully extracted page variable elements and their current states',
        pageVarsElements,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get page variable elements: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const validateYaml = createTool({
  id: 'validate-yaml',
  description: 'Validate YAML test script content against the automation testing schema. Performs syntax validation, schema compliance checking, and logical validation of test steps. Returns detailed error messages, warnings, and suggestions for fixing issues. Essential for ensuring test script quality before execution.',
  inputSchema: z.object({
    yamlContent: z.string().describe('YAML test script content to validate. Should contain test steps, selectors, actions, and assertions following the automation testing schema format.'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { yamlContent } = context;
      const page = manager.getPage();
      const result = await page.evaluate((yaml) => window.automationTesting.validateYaml(yaml), yamlContent);
      return {
        success: true,
        message: 'YAML validation completed',
        result,
        isValid: result?.isValid || false,
        errors: result?.errors || [],
        warnings: result?.warnings || [],
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to validate YAML: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const executeScript = createTool({
  id: 'execute-script',
  description: 'Execute custom JavaScript code on the connected browser page through page.evaluate method. This tool enables dynamic script execution for data extraction, DOM manipulation, validation, and page interaction operations. IMPORTANT: To retrieve results from the executed script, use "return" statements instead of "console.log". The script runs in the browser context and can access DOM elements, window objects, and any global variables available on the page.',
  inputSchema: z.object({
    script: z.string().describe('The JavaScript code to execute on the browser page. Use "return" to get results back (not console.log). Example: "return document.title" or "return window.location.href". The script has full access to the page\'s DOM and global scope.'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { script } = context;
      const page = manager.getPage();
      const result = await page.evaluate((script) => window.automationTesting.executeScript(script), script);
      return {
        success: true,
        message: 'Script executed successfully',
        result,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to execute script: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const executeYamlTest = createTool({
  id: 'execute-yaml-test',
  description: 'Execute a YAML-based automation test script on the connected browser page. Runs the test steps sequentially, performing actions like navigation, clicking, typing, and assertions. Returns detailed execution results including step-by-step status, timing, screenshots (if enabled), and any errors encountered.',
  inputSchema: z.object({
    yamlContent: z.string().describe('YAML test script content to execute. Must be valid YAML following the automation testing schema with test steps, actions, selectors, and assertions.'),
    options: z.object({
      timeout: z.number().optional().describe('Test execution timeout in milliseconds (default: 30000)'),
      continueOnFailure: z.boolean().optional().describe('Whether to continue executing remaining steps if a step fails (default: false)'),
      captureScreenshots: z.boolean().optional().describe('Whether to capture screenshots during test execution (default: false)'),
      verbose: z.boolean().optional().describe('Whether to enable verbose logging during execution (default: false)'),
    }).optional().describe('Optional execution configuration settings'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { yamlContent, options = {} } = context;
      const page = manager.getPage();
      const result = await page.evaluate(
        ({ yaml, opts }) => window.automationTesting.executeYamlTest(yaml, opts),
        { yaml: yamlContent, opts: options }
      );

      // Handle the result which could be a test ID string or test result object
      const isTestResult = typeof result === 'object' && result !== null;

      return {
        success: true,
        message: 'YAML test execution completed',
        result,
        testId: isTestResult ? (result as any)?.id || 'unknown' : result || 'unknown',
        status: isTestResult ? (result as any)?.status || 'unknown' : 'unknown',
        duration: isTestResult ? (result as any)?.duration || 0 : 0,
        steps: isTestResult ? (result as any)?.steps || [] : [],
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to execute YAML test: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const executeStepActionTest = createTool({
  id: 'execute-step-action-test',
  description: 'Execute a single step action test on the connected browser page. This tool runs individual test steps sequentially, performing UI automation actions such as navigation, element interaction (clicking, typing, selecting options, filling forms), and assertions. Supports both standard web elements and Element Plus UI components.',
  inputSchema: z.object({
    stepAction: z.object({}).passthrough().describe('Step action test object containing the test definition. Should be a parsed YAML object with test steps, element selectors, actions (click, type, fill, selectOption, wait, etc.), and expected assertions. Must follow the automation testing schema format.'),
    options: z.object({
      executorType: z.enum(['web', 'element-plus']).describe('Execution environment type: "web" for standard HTML elements, "element-plus" for Element Plus Vue.js component library'),
    }).optional().describe('Execution configuration options'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { stepAction, options = {} } = context;
      const page = manager.getPage();
      const result = await page.evaluate(({stepAction, options}) => window.automationTesting.executeStepActionTest(stepAction, options), {stepAction, options});
      return {
        success: true,
        message: 'Step action test executed successfully',
        result,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to execute step action test: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const executeTemplateTest = createTool({
  id: 'execute-template-test',
  description: 'Execute a template test on the connected browser page. Validates and runs a parameterized template with the provided parameter values. Templates define reusable test patterns with placeholders that are replaced with actual values during execution.',
  inputSchema: z.object({
    templateYaml: z.string().describe('Template YAML string containing the template definition. Must start with "templates:" followed by a template ID (e.g., "templates:\\n  search-form-template:"). The template can include parameters, steps with actions (selectOption, fill, click, wait, etc.), and use parameter placeholders like {{parameterName}}.'),
    parameters: z.object({}).passthrough().describe('Parameters object where the key is the template ID and the value is an object containing the parameter values. For example: {"search-form-template": {modelNumber: "XH01", sellCarNameOrCode: "12323"}}. The template ID must match the ID defined in the templateYaml.'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { templateYaml, parameters } = context;
      const page = manager.getPage();
      const result = await page.evaluate(({templateYaml, parameters}) => window.automationTesting.executeTemplateTest(templateYaml, parameters), {templateYaml, parameters});
      return {
        success: true,
        message: 'Template test executed successfully',
        result,
      };
    } catch (error) {
      return {  
        success: false,
        message: `Failed to execute template test: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const getTestResult = createTool({
  id: 'get-test-result',
  description: 'Retrieve the execution result of a previously run test by its test ID. Returns detailed information about test status, step results, timing, errors, and any captured screenshots.',
  inputSchema: z.object({
    testId: z.string().describe('Unique identifier of the test execution to retrieve results for'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { testId } = context;
      const page = manager.getPage();
      const result = await page.evaluate((id) => window.automationTesting.getTestResult(id), testId);
      return {
        success: true,
        message: result ? 'Test result retrieved successfully' : 'Test result not found',
        result,
        found: !!result,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get test result: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const getTestProgress = createTool({
  id: 'get-test-progress',
  description: 'Monitor the real-time execution progress of a running test. Returns current step information, completion percentage, elapsed time, and estimated remaining time.',
  inputSchema: z.object({
    testId: z.string().describe('Unique identifier of the test execution to monitor'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { testId } = context;
      const page = manager.getPage();
      const progress = await page.evaluate((id) => window.automationTesting.getTestProgress(id), testId);
      return {
        success: true,
        message: progress ? 'Test progress retrieved successfully' : 'Test progress not found',
        progress,
        found: !!progress,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get test progress: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const cancelTest = createTool({
  id: 'cancel-test',
  description: 'Cancel a currently running test execution. Stops the test immediately and marks it as cancelled. Useful for stopping long-running or problematic tests.',
  inputSchema: z.object({
    testId: z.string().describe('Unique identifier of the test execution to cancel'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { testId } = context;
      const page = manager.getPage();
      const cancelled = await page.evaluate((id) => window.automationTesting.cancelTest(id), testId);
      return {
        success: true,
        message: cancelled ? 'Test cancelled successfully' : 'Failed to cancel test (may not be running)',
        cancelled,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to cancel test: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const getRunningTests = createTool({
  id: 'get-running-tests',
  description: 'Get a list of all currently running test executions. Returns test IDs and basic information about active tests, useful for monitoring and management.',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      await manager.ensureConnected();
      const page = manager.getPage();
      const runningTests = await page.evaluate(() => window.automationTesting.getRunningTests());
      return {
        success: true,
        message: `Found ${runningTests?.length || 0} running tests`,
        runningTests: runningTests || [],
        count: runningTests?.length || 0,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get running tests: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const getTestHistory = createTool({
  id: 'get-test-history',
  description: 'Retrieve the history of previously executed tests. Returns a list of test executions with their results, timing, and metadata. Useful for analysis and debugging.',
  inputSchema: z.object({
    limit: z.number().optional().describe('Maximum number of test records to return (default: 10, max: 100)'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { limit = 10 } = context;
      const page = manager.getPage();
      const history = await page.evaluate((lmt) => window.automationTesting.getTestHistory(lmt), limit);
      return {
        success: true,
        message: `Retrieved ${history?.length || 0} test history records`,
        history: history || [],
        count: history?.length || 0,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get test history: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const clearTestHistory = createTool({
  id: 'clear-test-history',
  description: 'Clear all stored test execution history. This permanently removes all historical test data and cannot be undone. Use with caution.',
  inputSchema: z.object({
    confirm: z.boolean().describe('Confirmation flag - must be set to true to proceed with clearing history'),
  }),
  execute: async ({ context }) => {
    try {
      await manager.ensureConnected();
      const { confirm } = context;

      if (!confirm) {
        return {
          success: false,
          message: 'History clearing cancelled - confirmation flag must be set to true',
        };
      }

      const page = manager.getPage();
      const cleared = await page.evaluate(() => window.automationTesting.clearTestHistory());
      return {
        success: true,
        message: cleared ? 'Test history cleared successfully' : 'Failed to clear test history',
        cleared,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to clear test history: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const disconnectBrowser = createTool({
  id: 'disconnect-browser',
  description: 'Safely disconnect from the current browser session and perform cleanup operations. This tool closes the active browser connection, releases all allocated resources (browser context, pages), and resets the automation manager to its initial state. Should be called when automation tasks are complete or when switching to a different browser instance.',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      await manager.disconnect();
      return {
        success: true,
        message: 'Successfully disconnected from browser and cleaned up resources',
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to disconnect from browser: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

export const saveYamlFile = createTool({
  id: 'save-yaml-file',
  description: 'Save a YAML file to the local file system. This tool allows you to save a YAML file to the local file system for later use.',
  inputSchema: z.object({
    yamlContent: z.string().describe('YAML content to save to the file. Must be valid YAML following the automation testing schema with test steps, actions, selectors, and assertions. The file will be saved in the templates folder. '),
    fileName: z.string().describe('Name of the file to save. The file will be saved in the templates folder.'),
    overwrite: z.boolean().describe('Whether to overwrite the existing file. If true, the existing file will be overwritten. If false, the file will not be overwritten and the tool will return an error.'),
  }),
  execute: async ({ context }) => {
    try {

      const { yamlContent, fileName, overwrite } = context;
      const rootDir = "D:\\works\\practice\\testing-agent\\templates"
      const filePath = path.join(rootDir, fileName);
      const isExist = fs.existsSync(filePath);
      if (isExist && !overwrite) {
        return {
          success: false,
          message: 'File already exists',
          suggestion: `Please use a different file name or delete the existing file.`,
        };
      } else if (isExist && overwrite) {
        fs.unlinkSync(filePath);
      }
      fs.writeFileSync(filePath, yamlContent);
      return {
        success: true,
        message: 'YAML file saved successfully',
        filePath,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to save YAML file: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

declare global {
  interface Window {
    automationTesting: any;
  }
}

