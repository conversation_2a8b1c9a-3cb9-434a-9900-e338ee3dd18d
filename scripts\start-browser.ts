import { chromium } from 'playwright';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface BrowserInfo {
  wsEndpoint: string;
  browser: any;
  context: any;
  page: any;
}

/**
 * 启动一个新的浏览器实例用于远程连接
 */
export async function startRemoteBrowser(options: {
  headless?: boolean;
  port?: number;
  userDataDir?: string;
} = {}): Promise<BrowserInfo> {
  const { headless = false, port = 9222, userDataDir } = options;

  try {
    // 启动 Chromium 浏览器
    const browser = await chromium.launch({
      headless,
      args: [
        `--remote-debugging-port=${port}`,
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-extensions',
        '--disable-default-apps',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        ...(userDataDir ? [`--user-data-dir=${userDataDir}`] : []),
      ],
    });

    // 获取 WebSocket endpoint
    const wsEndpoint = browser.wsEndpoint();
    
    // 创建上下文和页面
    const context = await browser.newContext();
    const page = await context.newPage();

    console.log(`✅ Browser started successfully!`);
    console.log(`📡 WebSocket Endpoint: ${wsEndpoint}`);
    console.log(`🌐 Remote debugging port: ${port}`);
    
    return {
      wsEndpoint,
      browser,
      context,
      page,
    };
  } catch (error) {
    console.error('❌ Failed to start browser:', error);
    throw error;
  }
}

/**
 * 获取现有浏览器的 WebSocket endpoints
 */
export async function getBrowserEndpoints(port: number = 9222): Promise<string[]> {
  try {
    const response = await fetch(`http://localhost:${port}/json/version`);
    const data = await response.json();
    return [data.webSocketDebuggerUrl];
  } catch (error) {
    console.error(`No browser found on port ${port}`);
    return [];
  }
}

/**
 * 连接到现有的浏览器实例
 */
export async function connectToExistingBrowser(wsEndpoint: string): Promise<BrowserInfo> {
  try {
    const browser = await chromium.connectOverCDP(wsEndpoint);
    const contexts = browser.contexts();
    const context = contexts.length > 0 ? contexts[0] : await browser.newContext();
    const pages = context.pages();
    const page = pages.length > 0 ? pages[0] : await context.newPage();

    console.log(`✅ Connected to existing browser!`);
    console.log(`📡 WebSocket Endpoint: ${wsEndpoint}`);

    return {
      wsEndpoint,
      browser,
      context,
      page,
    };
  } catch (error) {
    console.error('❌ Failed to connect to existing browser:', error);
    throw error;
  }
}

/**
 * 启动系统浏览器（Chrome/Edge）并启用远程调试
 */
export async function startSystemBrowser(port: number = 9222): Promise<string> {
  const chromePaths = [
    'google-chrome',
    'chrome',
    'chromium',
    'google-chrome-stable',
    '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
    'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
    'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
    '/usr/bin/google-chrome',
    '/usr/bin/chromium-browser',
  ];

  for (const chromePath of chromePaths) {
    try {
      const command = `"${chromePath}" --remote-debugging-port=${port} --no-first-run --disable-extensions`;
      await execAsync(command);
      
      // 等待浏览器启动
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 获取 WebSocket endpoint
      const endpoints = await getBrowserEndpoints(port);
      if (endpoints.length > 0) {
        console.log(`✅ System browser started on port ${port}`);
        console.log(`📡 WebSocket Endpoint: ${endpoints[0]}`);
        return endpoints[0];
      }
    } catch (error) {
      continue; // 尝试下一个路径
    }
  }
  
  throw new Error('❌ Could not start system browser. Please install Chrome or Chromium.');
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2] || 'start';
  const port = parseInt(process.argv[3]) || 9222;

  switch (command) {
    case 'start':
      startRemoteBrowser({ port }).then((info) => {
        console.log('\n🚀 Browser is ready for remote connections!');
        console.log('\n📋 Copy this WebSocket endpoint for your tools:');
        console.log(`   ${info.wsEndpoint}`);
        console.log('\n⚠️  Keep this terminal open to maintain the browser session.');
        
        // 保持进程运行
        process.on('SIGINT', async () => {
          console.log('\n🛑 Shutting down browser...');
          await info.browser.close();
          process.exit(0);
        });
      }).catch(console.error);
      break;

    case 'system':
      startSystemBrowser(port).then((endpoint) => {
        console.log('\n🚀 System browser started!');
        console.log('\n📋 Copy this WebSocket endpoint for your tools:');
        console.log(`   ${endpoint}`);
      }).catch(console.error);
      break;

    case 'list':
      getBrowserEndpoints(port).then((endpoints) => {
        if (endpoints.length > 0) {
          console.log('🔍 Found running browsers:');
          endpoints.forEach((endpoint, index) => {
            console.log(`   ${index + 1}. ${endpoint}`);
          });
        } else {
          console.log(`❌ No browsers found on port ${port}`);
        }
      }).catch(console.error);
      break;

    default:
      console.log(`
🤖 Browser Management Script

Usage:
  npm run browser [command] [port]

Commands:
  start   - Start a new Playwright browser (default)
  system  - Start system Chrome with remote debugging
  list    - List running browsers with remote debugging

Examples:
  npm run browser start 9222
  npm run browser system 9223
  npm run browser list 9222
      `);
  }
} 