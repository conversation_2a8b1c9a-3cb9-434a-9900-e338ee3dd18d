#!/usr/bin/env node
import { MCPServer } from "@mastra/mcp";
import {  connectBrowser,
    navigateToUrl,
    getYamlSchema,
    getSnapshotForAI,
    getSourceCodeFile,
    validateYaml,
    executeScript,
    executeYamlTest,
    executeTemplateTest,
    executeStepActionTest,
    disconnectBrowser,
    saveYamlFile, } from "./tools/automation-tool";
 
const server = new MCPServer({
  name: "automation-mcp-server",
  version: "1.0.0",
  tools: {  connectBrowser,
    navigateToUrl,
    getYamlSchema,
    getSnapshotForAI,
    getSourceCodeFile,
    validateYaml,
    executeScript,
    executeYamlTest,
    executeTemplateTest,
    executeStepActionTest,
    disconnectBrowser,
    saveYamlFile, },
});
 
server.startStdio().catch((error) => {
  console.error("Error running MCP server:", error);
  process.exit(1);
});