config:
  name: "驾驶舱管理测试套件"
  description: "驾驶舱管理的新增和删除功能测试"
  baseUrl: "http://************:9999"
  executorType: "element-plus"
  testMode: "flow"
  timeout: 30000
  variables:
    cockpitData:
      name: "测试驾驶舱003"
      machineCode: "TEST111222333ABCDEF"
    deleteCockpitData:
      name: "测试驾驶舱003"

templates:
  cockpit-add-template:
    name: "驾驶舱新增模板"
    type: "shared"
    parameters:
      - name: cockpitName
        type: "string"
        required: true
      - name: machineCode
        type: "string"
        required: true
    steps:
      - action: click
        role: button
        roleOptions:
          name: "新增驾驶舱"
      - action: fill
        role: textbox
        roleOptions:
          name: "驾驶舱名称"
        within:
          role: dialog
          roleOptions:
            name: "新增驾驶舱"
        data: "{{cockpitName}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "机器码"
        within:
          role: dialog
          roleOptions:
            name: "新增驾驶舱"
        data: "{{machineCode}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 1000

  cockpit-delete-template:
    name: "驾驶舱删除模板"
    type: "shared"
    parameters:
      - name: cockpitName
        type: "string"
        required: true
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "deleteRow"
          fields:
            - column: "名称"
              value: "{{cockpitName}}"
              exact: true
          timeout: 1000

tests:
  - name: "驾驶舱新增测试"
    description: "测试驾驶舱新增功能"
    steps:
      - action: useTemplate
        template: "cockpit-add-template"
        parameters:
          cockpitName: "{{cockpitData.name}}"
          machineCode: "{{cockpitData.machineCode}}"

  - name: "驾驶舱删除测试"
    description: "测试驾驶舱删除功能"
    steps:
      - action: useTemplate
        template: "cockpit-delete-template"
        parameters:
          cockpitName: "{{deleteCockpitData.name}}"