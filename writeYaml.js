import fs from 'fs';
import path from 'path';

const yaml  = "config:\n  name: \"充电桩管理测试套件\"\n  description: \"充电桩管理页面的新增、删除功能测试\"\n  baseUrl: \"http://************:9999\"\n  executorType: \"element-plus\"\n  testMode: \"flow\"\n  timeout: 30000\n\n# 统一表单数据配置变量\nvariables:\n  testData:\n    # 新增充电桩测试数据\n    addPileData:\n      pileName: \"自动化测试充电桩\"\n      pileCode: \"AUTO_TEST_PILE_NEW\"\n      modelNumber: \"测试型号X2\"\n      interfaceCodeA: \"INTERFACE_A_001\"\n      interfaceCodeB: \"INTERFACE_B_001\"\n    \n    # 删除充电桩测试数据\n    deletePileData:\n      pileName: \"自动化测试充电桩\"\n\ntemplates:\n  add-charge-pile-template:\n    name: \"新增充电桩模板\"\n    type: \"shared\"\n    parameters:\n      - name: pileName\n        type: \"string\"\n        required: true\n      - name: pileCode\n        type: \"string\"\n        required: true\n      - name: modelNumber\n        type: \"string\"\n        required: false\n      - name: interfaceCodeA\n        type: \"string\"\n        required: false\n      - name: interfaceCodeB\n        type: \"string\"\n        required: false\n    steps:\n      - action: click\n        role: button\n        roleOptions:\n          name: \"新增充电桩\"\n      - action: fill\n        role: textbox\n        roleOptions:\n          name: \"充电桩名称\"\n        within:\n          role: dialog\n          roleOptions:\n            name: \"新增充电桩\"\n        data: \"{{pileName}}\"\n      - action: fill\n        role: textbox\n        roleOptions:\n          name: \"充电桩编码\"\n        within:\n          role: dialog\n          roleOptions:\n            name: \"新增充电桩\"\n        data: \"{{pileCode}}\"\n      - action: fill\n        role: textbox\n        roleOptions:\n          name: \"充电桩型号\"\n        within:\n          role: dialog\n          roleOptions:\n            name: \"新增充电桩\"\n        data: \"{{modelNumber}}\"\n      - action: fill\n        role: textbox\n        roleOptions:\n          name: \"1#充电接口编码\"\n        within:\n          role: dialog\n          roleOptions:\n            name: \"新增充电桩\"\n        data: \"{{interfaceCodeA}}\"\n      - action: fill\n        role: textbox\n        roleOptions:\n          name: \"2#充电接口编码\"\n        within:\n          role: dialog\n          roleOptions:\n            name: \"新增充电桩\"\n        data: \"{{interfaceCodeB}}\"\n      - action: useScript\n        script: \"dialog-operations\"\n        parameters:\n          action: \"confirmDialog\"\n          timeout: 2000\n\n  delete-charge-pile-template:\n    name: \"删除充电桩模板\"\n    type: \"shared\"\n    parameters:\n      - name: pileName\n        type: \"string\"\n        required: true\n    steps:\n      - action: useScript\n        script: \"table-operations\"\n        parameters:\n          action: \"deleteRow\"\n          fields:\n            - column: \"名称\"\n              value: \"{{pileName}}\"\n              exact: true\n          timeout: 1000\n\ntests:\n  - name: \"充电桩新增功能测试\"\n    description: \"测试充电桩的新增功能\"\n    steps:\n      - action: navigate\n        url: \"/#/deviceManage/chargeManage/index\"\n      - action: useTemplate\n        template: \"add-charge-pile-template\"\n        parameters:\n          pileName: \"{{testData.addPileData.pileName}}\"\n          pileCode: \"{{testData.addPileData.pileCode}}\"\n          modelNumber: \"{{testData.addPileData.modelNumber}}\"\n          interfaceCodeA: \"{{testData.addPileData.interfaceCodeA}}\"\n          interfaceCodeB: \"{{testData.addPileData.interfaceCodeB}}\"\n      - action: verify\n        type: element\n        role: cell\n        roleOptions:\n          name: \"{{testData.addPileData.pileName}}\"\n        assertion: visible\n\n  - name: \"充电桩删除功能测试\"\n    description: \"测试充电桩的删除功能\"\n    steps:\n      - action: useTemplate\n        template: \"delete-charge-pile-template\"\n        parameters:\n          pileName: \"{{testData.deletePileData.pileName}}\"\n      - action: verify\n        type: element\n        role: cell\n        roleOptions:\n          name: \"{{testData.deletePileData.pileName}}\"\n        assertion: hidden\n\n  - name: \"充电桩完整流程测试\"\n    description: \"测试充电桩的完整新增和删除流程\"\n    steps:\n      - action: navigate\n        url: \"/#/deviceManage/chargeManage/index\"\n      - action: useTemplate\n        template: \"add-charge-pile-template\"\n        parameters:\n          pileName: \"{{testData.addPileData.pileName}}\"\n          pileCode: \"{{testData.addPileData.pileCode}}\"\n          modelNumber: \"{{testData.addPileData.modelNumber}}\"\n          interfaceCodeA: \"{{testData.addPileData.interfaceCodeA}}\"\n          interfaceCodeB: \"{{testData.addPileData.interfaceCodeB}}\"\n      - action: verify\n        type: element\n        role: cell\n        roleOptions:\n          name: \"{{testData.addPileData.pileName}}\"\n        assertion: visible\n      - action: wait\n        data: 1000\n      - action: useTemplate\n        template: \"delete-charge-pile-template\"\n        parameters:\n          pileName: \"{{testData.deletePileData.pileName}}\"\n      - action: verify\n        type: element\n        role: cell\n        roleOptions:\n          name: \"{{testData.deletePileData.pileName}}\"\n        assertion: hidden"

fs.writeFileSync('test.yaml', yaml);

console.log('done');