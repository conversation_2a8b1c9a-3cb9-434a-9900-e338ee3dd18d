config:
  name: "充电桩管理测试套件"
  description: "充电桩管理页面的新增、删除功能测试"
  baseUrl: "http://************:9999"
  executorType: "element-plus"
  testMode: "flow"
  timeout: 30000

  # 统一表单数据配置变量
  variables:
    testData:
      # 新增充电桩测试数据
      addPileData:
        pileName: "自动化测试充电桩"
        pileCode: "AUTO_TEST_PILE_NEW"
        modelNumber: "测试型号X2"
        interfaceCodeA: "INTERFACE_A_001"
        interfaceCodeB: "INTERFACE_B_001"
      
      # 删除充电桩测试数据
      deletePileData:
        pileName: "自动化测试充电桩"

templates:
  add-charge-pile-template:
    name: "新增充电桩模板"
    type: "shared"
    parameters:
      - name: pileName
        type: "string"
        required: true
      - name: pileCode
        type: "string"
        required: true
      - name: modelNumber
        type: "string"
        required: false
      - name: interfaceCodeA
        type: "string"
        required: false
      - name: interfaceCodeB
        type: "string"
        required: false
    steps:
      - action: click
        role: button
        roleOptions:
          name: "新增充电桩"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩名称"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{pileName}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{pileCode}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩型号"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{modelNumber}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "1#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{interfaceCodeA}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "2#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{interfaceCodeB}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 2000

  delete-charge-pile-template:
    name: "删除充电桩模板"
    type: "shared"
    parameters:
      - name: pileName
        type: "string"
        required: true
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "deleteRow"
          fields:
            - column: "名称"
              value: "{{pileName}}"
              exact: true
          timeout: 1000

tests:
  - name: "充电桩新增功能测试"
    description: "测试充电桩的新增功能"
    steps:
      - action: navigate
        url: "/#/deviceManage/chargeManage/index"
      - action: useTemplate
        template: "add-charge-pile-template"
        parameters:
          pileName: "{{testData.addPileData.pileName}}"
          pileCode: "{{testData.addPileData.pileCode}}"
          modelNumber: "{{testData.addPileData.modelNumber}}"
          interfaceCodeA: "{{testData.addPileData.interfaceCodeA}}"
          interfaceCodeB: "{{testData.addPileData.interfaceCodeB}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.addPileData.pileName}}"
        assertion: visible

  - name: "充电桩删除功能测试"
    description: "测试充电桩的删除功能"
    steps:
      - action: useTemplate
        template: "delete-charge-pile-template"
        parameters:
          pileName: "{{testData.deletePileData.pileName}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.deletePileData.pileName}}"
        assertion: hidden

  - name: "充电桩完整流程测试"
    description: "测试充电桩的完整新增和删除流程"
    steps:
      - action: navigate
        url: "/#/deviceManage/chargeManage/index"
      - action: useTemplate
        template: "add-charge-pile-template"
        parameters:
          pileName: "{{testData.addPileData.pileName}}"
          pileCode: "{{testData.addPileData.pileCode}}"
          modelNumber: "{{testData.addPileData.modelNumber}}"
          interfaceCodeA: "{{testData.addPileData.interfaceCodeA}}"
          interfaceCodeB: "{{testData.addPileData.interfaceCodeB}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.addPileData.pileName}}"
        assertion: visible
      - action: wait
        data: 1000
      - action: useTemplate
        template: "delete-charge-pile-template"
        parameters:
          pileName: "{{testData.deletePileData.pileName}}"
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testData.deletePileData.pileName}}"
        assertion: hidden