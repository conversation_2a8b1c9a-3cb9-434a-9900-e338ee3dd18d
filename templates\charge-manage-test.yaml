config:
  name: "充电桩管理测试"
  executorType: "element-plus"
  variables:
    chargePile:
      name: "自动化测试充电桩"
      code: "auto_test_code_001"
      model: "C-Auto"
      port1: "auto-port-1"
      port2: "auto-port-2"

templates:
  add-charge-pile-template:
    name: "新增充电桩模板"
    parameters:
      - name: chargeName
        type: "string"
      - name: chargeCode
        type: "string"
      - name: chargeModel
        type: "string"
      - name: chargePort1
        type: "string"
      - name: chargePort2
        type: "string"
    steps:
      - action: click
        role: button
        roleOptions:
          name: "新增充电桩"
      - action: fill
        role: textbox
        roleOptions:
          name: "* 充电桩名称"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{chargeName}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "* 充电桩编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{chargeCode}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩型号"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{chargeModel}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "1#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{chargePort1}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "2#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        data: "{{chargePort2}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          actionType: "confirm"

  delete-charge-pile-template:
    name: "删除充电桩模板"
    parameters:
      - name: chargeCode
        type: "string"
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          actionType: "delete"
          fields:
            - column: "编码"
              value: "{{chargeCode}}"

tests:
  - name: "新增充电桩测试"
    description: "测试新增充电桩功能"
    steps:
      - action: useTemplate
        template: add-charge-pile-template
        parameters:
          chargeName: "{{chargePile.name}}"
          chargeCode: "{{chargePile.code}}"
          chargeModel: "{{chargePile.model}}"
          chargePort1: "{{chargePile.port1}}"
          chargePort2: "{{chargePile.port2}}"

hooks:
  afterAll:
    - name: "清理测试数据"
      description: "删除新增的充电桩"
      action: useTemplate
      template: delete-charge-pile-template
      parameters:
        chargeCode: "{{chargePile.code}}"
