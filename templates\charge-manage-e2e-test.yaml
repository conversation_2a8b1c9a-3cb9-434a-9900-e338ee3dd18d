config:
  name: "充电桩管理测试"
  description: "针对充电桩管理模块进行新增和删除操作的端到端测试"
  executorType: "element-plus"
  variables:
    chargeData:
      name: "Test-Charge-04"
      code: "Test-Code-04"
      modelNumber: "Test-Model-04"
      interfaceCodeA: "Port-07"
      interfaceCodeB: "Port-08"

templates:
  create-charge-template:
    name: "新增充电桩模板"
    steps:
      - action: click
        role: button
        roleOptions:
          name: "新增充电桩"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩名称"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        value: "{{chargeData.name}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        value: "{{chargeData.code}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "充电桩型号"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        value: "{{chargeData.modelNumber}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "1#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        value: "{{chargeData.interfaceCodeA}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "2#充电接口编码"
        within:
          role: dialog
          roleOptions:
            name: "新增充电桩"
        value: "{{chargeData.interfaceCodeB}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          actionType: "confirm"

  delete-charge-template:
    name: "删除充电桩模板"
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          actionType: "delete"
          fields:
            - column: "名称"
              value: "{{chargeData.name}}"

tests:
  - name: "新增并验证充电桩"
    description: "测试新增充电桩功能，并验证数据是否正确显示在表格中"
    steps:
      - action: useTemplate
        template: "create-charge-template"
      - action: wait
        data: 2000 # 等待2秒，确保数据加载完成
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{chargeData.name}}"
        assertion: visible
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{chargeData.code}}"
        assertion: visible

afterAll:
  - name: "清理测试数据"
    description: "删除测试中创建的充电桩数据"
    action: useTemplate
    template: "delete-charge-template"
