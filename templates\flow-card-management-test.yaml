
config:
  name: "流量卡管理测试"
  description: "流量卡管理页面的自动化测试"
  executorType: "element-plus"
  variables:
    cardCode: "test-card-new-001-1715853600"
    iccid: "12345678901234567890-1715853600"
    deviceIndex: 1
    editedIccid: "12345678901234567890-edited"
    editedDeviceIndex: 2

templates:
  add-flow-card-template:
    name: "新增流量卡模板"
    parameters:
      - name: cardCode
        type: "string"
        required: true
      - name: iccid
        type: "string"
        required: true
      - name: deviceIndex
        type: "number"
        required: true
    steps:
      - action: click
        role: button
        roleOptions:
          name: "添加"
      - action: fill
        role: textbox
        roleOptions:
          name: "流量卡编码"
        within:
          role: dialog
          roleOptions:
            name: "新增流量卡"
        data: "{{cardCode}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "ICCID"
        within:
          role: dialog
          roleOptions:
            name: "新增流量卡"
        data: "{{iccid}}"
      - action: selectOption
        role: combobox
        roleOptions:
          name: "所属设备"
        within:
          role: dialog
          roleOptions:
            name: "新增流量卡"
        data:
          index: "{{deviceIndex}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          actionType: "confirm"

  query-flow-card-template:
    name: "查询流量卡模板"
    parameters:
      - name: cardCode
        type: "string"
        required: true
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "流量卡名称/编码"
        data: "{{cardCode}}"
      - action: click
        role: button
        roleOptions:
          name: "查询"
      - action: wait
        data: 1000
      - action: useScript
        script: "table-operations"
        parameters:
          actionType: "findRows"
          fields:
            - column: "编码"
              value: "{{cardCode}}"
      - action: click
        role: button
        roleOptions:
          name: "重置"

  delete-flow-card-template:
    name: "删除流量卡模板"
    parameters:
      - name: cardCode
        type: "string"
        required: true
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          actionType: "deleteRow"
          fields:
            - column: "编码"
              value: "{{cardCode}}"

tests:
  - name: "新增和查询流量卡"
    description: "测试新增和查询流量卡的功能"
    steps:
      - action: useTemplate
        template: "add-flow-card-template"
        parameters:
          cardCode: "{{cardCode}}"
          iccid: "{{iccid}}"
          deviceIndex: "{{deviceIndex}}"
      - action: useTemplate
        template: "query-flow-card-template"
        parameters:
          cardCode: "{{cardCode}}"

afterAll:
  - action: useTemplate
    template: "delete-flow-card-template"
    parameters:
      cardCode: "{{cardCode}}"
