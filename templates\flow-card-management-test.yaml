config:
  name: "流量卡管理测试套件"
  description: "流量卡管理的完整测试流程，包括新增、删除、查询、编辑功能"
  executorType: "element-plus"
  testMode: "flow"
  timeout: 30000
  captureScreenshots: true
  variables:
    flowCard:
      add:
        cardNo: "TEST002"
        cardIccid: "987654321098765432109"
        deviceIndex: 0
      edit:
        cardNo: "card00001"
        newCardIccid: "111222333444555666777"
        newDeviceIndex: 1
      delete:
        cardNo: "TEST002"
      search:
        searchKeyword: "card"

templates:
  add-flow-card-template:
    name: "新增流量卡模板"
    parameters:
      - name: cardNo
        type: "string"
        required: true
      - name: cardIccid
        type: "string"
        required: true
      - name: deviceIndex
        type: "number"
        required: false
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "流量卡编码"
        within:
          role: dialog
          roleOptions:
            name: "新增流量卡"
        data: "{{cardNo}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "ICCID"
        within:
          role: dialog
          roleOptions:
            name: "新增流量卡"
        data: "{{cardIccid}}"
      - action: selectOption
        role: combobox
        roleOptions:
          name: "所属设备"
        within:
          role: dialog
          roleOptions:
            name: "新增流量卡"
        index: "{{deviceIndex}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 2000

  delete-flow-card-template:
    name: "删除流量卡模板"
    parameters:
      - name: cardNo
        type: "string"
        required: true
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "deleteRow"
          fields:
            - column: "编码"
              value: "{{cardNo}}"
              exact: true
          timeout: 3000

  search-flow-card-template:
    name: "查询流量卡模板"
    parameters:
      - name: searchKeyword
        type: "string"
        required: false
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "流量卡名称/编码"
        data: "{{searchKeyword}}"
      - action: click
        role: button
        roleOptions:
          name: "查询"
      - action: wait
        data: 1000
      - action: click
        role: button
        roleOptions:
          name: "重置"

  edit-flow-card-template:
    name: "编辑流量卡模板"
    parameters:
      - name: cardNo
        type: "string"
        required: true
      - name: newCardIccid
        type: "string"
        required: false
      - name: newDeviceIndex
        type: "number"
        required: false
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "editRow"
          fields:
            - column: "编码"
              value: "{{cardNo}}"
              exact: true
          timeout: 3000
      - action: fill
        role: textbox
        roleOptions:
          name: "ICCID"
        within:
          role: dialog
          roleOptions:
            name: "修改流量卡"
        data: "{{newCardIccid}}"
      - action: selectOption
        role: combobox
        roleOptions:
          name: "所属设备"
        within:
          role: dialog
          roleOptions:
            name: "修改流量卡"
        index: "{{newDeviceIndex}}"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 2000

tests:
  - name: "流量卡新增功能测试"
    description: "测试流量卡新增功能"
    steps:
      - action: click
        role: button
        roleOptions:
          name: "添加"
      - action: useTemplate
        template: "add-flow-card-template"
        parameters:
          cardNo: "{{flowCard.add.cardNo}}"
          cardIccid: "{{flowCard.add.cardIccid}}"
          deviceIndex: "{{flowCard.add.deviceIndex}}"

  - name: "流量卡查询功能测试"
    description: "测试流量卡查询功能"
    steps:
      - action: useTemplate
        template: "search-flow-card-template"
        parameters:
          searchKeyword: "{{flowCard.search.searchKeyword}}"

  - name: "流量卡编辑功能测试"
    description: "测试流量卡编辑功能"
    steps:
      - action: useTemplate
        template: "edit-flow-card-template"
        parameters:
          cardNo: "{{flowCard.edit.cardNo}}"
          newCardIccid: "{{flowCard.edit.newCardIccid}}"
          newDeviceIndex: "{{flowCard.edit.newDeviceIndex}}"

  - name: "流量卡删除功能测试"
    description: "测试流量卡删除功能"
    steps:
      - action: useTemplate
        template: "delete-flow-card-template"
        parameters:
          cardNo: "{{flowCard.delete.cardNo}}"