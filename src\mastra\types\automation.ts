/**
 * 页面API接口类型定义
 * 定义通过page.exposeFunction挂载到页面的函数签名和数据结构
 */

/**
 * 测试执行状态
 */
export type TestStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

/**
 * 测试步骤状态
 */
export type StepStatus = 'pending' | 'running' | 'completed' | 'failed' | 'skipped';

/**
 * 测试执行选项
 */
export interface TestExecutionOptions {
  /** 测试超时时间（毫秒） */
  timeout?: number;
  /** 是否在失败时继续执行 */
  continueOnFailure?: boolean;
  /** 是否截图 */
  captureScreenshots?: boolean;
  /** 是否记录详细日志 */
  verbose?: boolean;
}

/**
 * 测试步骤进度信息
 */
export interface TestStepProgress {
  /** 步骤索引 */
  stepIndex: number;
  /** 步骤动作 */
  action: string;
  /** 选择器 */
  selector?: string;
  /** 步骤状态 */
  status: StepStatus;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime?: number;
  /** 执行时长（毫秒） */
  duration?: number;
  /** 错误信息 */
  error?: string;
  /** 截图路径 */
  screenshot?: string;
}

/**
 * 测试执行进度
 */
export interface TestProgress {
  /** 测试ID */
  testId: string;
  /** 测试状态 */
  status: TestStatus;
  /** 当前步骤索引 */
  currentStep: number;
  /** 总步骤数 */
  totalSteps: number;
  /** 当前执行的动作 */
  currentAction?: string;
  /** 当前选择器 */
  currentSelector?: string;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime?: number;
  /** 总执行时长（毫秒） */
  duration?: number;
  /** 步骤执行结果 */
  stepResults: TestStepProgress[];
  /** 执行日志 */
  logs: string[];
  /** 截图列表 */
  screenshots: string[];
  /** 进度百分比 (0-100) */
  progressPercentage: number;
}

/**
 * 测试执行结果
 */
export interface TestResult {
  /** 测试ID */
  testId: string;
  /** 测试名称 */
  testName?: string;
  /** 是否成功 */
  success: boolean;
  /** 测试状态 */
  status: TestStatus;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 总执行时长（毫秒） */
  duration: number;
  /** 成功步骤数 */
  successSteps: number;
  /** 失败步骤数 */
  failedSteps: number;
  /** 总步骤数 */
  totalSteps: number;
  /** 步骤执行结果 */
  stepResults: TestStepProgress[];
  /** 错误信息 */
  error?: string;
  /** 执行日志 */
  logs: string[];
  /** 截图列表 */
  screenshots: string[];
  /** 生成的报告路径 */
  reportPath?: string;
}

/**
 * 验证错误详情
 */
export interface ValidationError {
  type: 'SYNTAX' | 'SCHEMA' | 'STRUCTURE' | 'LOGIC';
  severity: 'ERROR' | 'WARNING' | 'INFO';
  message: string;
  location?: {
    line: number;
    column: number;
    path?: string;
  };
  suggestions: string[];
}

/**
 * 验证结果
 */
export interface ValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息列表 */
  errors: ValidationError[];
  /** 警告信息列表 */
  warnings: ValidationError[];
}

/**
 * 测试执行记录
 */
export interface TestExecution {
  /** 测试ID */
  testId: string;
  /** 测试名称 */
  testName?: string;
  /** YAML内容 */
  yamlContent: string;
  /** 执行结果 */
  result: TestResult;
  /** 创建时间 */
  createdAt: number;
}

/**
 * 页面自动化测试API接口
 * 这些函数将通过page.exposeFunction挂载到页面的window对象上
 */
export interface PageAutomationAPI {
  /**
   * 执行YAML测试
   * @param yamlContent YAML测试内容
   * @param options 执行选项
   * @returns 测试ID，用于后续查询结果和进度
   */
  executeYamlTest(yamlContent: string, options?: TestExecutionOptions): Promise<string>;

  /**
   * 执行脚本
   * @param script 脚本内容
   * @returns 执行结果
   */
  executeScript(script: string): Promise<any>;

  /**
   * 获取测试结果
   * @param testId 测试ID
   * @returns 测试执行结果
   */
  getTestResult(testId: string): Promise<TestResult | null>;

  /**
   * 获取测试进度
   * @param testId 测试ID
   * @returns 测试执行进度
   */
  getTestProgress(testId: string): Promise<TestProgress | null>;

  /**
   * 取消测试执行
   * @param testId 测试ID
   * @returns 是否成功取消
   */
  cancelTest(testId: string): Promise<boolean>;

  /**
   * 验证YAML内容
   * @param yamlContent YAML内容
   * @returns 验证结果
   */
  validateYaml(yamlContent: string): Promise<ValidationResult>;

  /**
   * 获取正在运行的测试列表
   * @returns 运行中的测试ID列表
   */
  getRunningTests(): Promise<string[]>;

  /**
   * 获取测试历史记录
   * @param limit 返回记录数量限制
   * @returns 测试执行历史
   */
  getTestHistory(limit?: number): Promise<TestExecution[]>;

  /**
   * 清除测试历史记录
   * @returns 是否成功清除
   */
  clearTestHistory(): Promise<boolean>;

  /**
   * 获取页面的AI快照
   * 通过 PageEx 类型的 _snapshotForAI 方法获取页面的可访问性树快照
   * @returns 页面快照字符串
   */
  getSnapshotForAI(): Promise<string>;

  /**
   * 获取 YAML Schema 定义
   * 为 LLM 生成测试脚本提供 schema 格式说明
   * @returns YAML Schema 定义对象
   */
  getYAMLSchema(): Promise<any>;

  /**
   * 获取页面变量元素
   * @returns 页面变量元素
   */
  getPageVarsElements(): Promise<any>;

  /**
   * 获取页面变量元素
   * @returns 页面变量元素
   */
  executeStepActionTest(stepAction: any, options: any): Promise<any>;

  /**
   * 执行模板测试
   * @returns 页面变量元素
   */
  executeTemplateTest(templateYaml: string, parameters: any): Promise<any>;

  /**
   * 获取页面变量元素
   * @returns 页面变量元素
   */
  getSourceCodeFile(filePath: string): Promise<string>;
}

/**
 * 扩展Window接口，添加自动化测试API
 */
declare global {
  interface Window {
    automationTesting: PageAutomationAPI;
  }
}
