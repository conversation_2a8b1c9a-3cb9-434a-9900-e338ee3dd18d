# Playwright Tools and Agent Guide

This guide explains how to use the Playwright tools and agent to automate browser interactions and execute window global methods.

## Overview

The Playwright integration provides:
- **Playwright Tools**: Individual tools for specific browser operations
- **Playwright Agent**: An intelligent agent that can use all tools through natural language commands
- **Browser Connection**: Connect to browsers via WebSocket endpoints
- **Window Method Execution**: Execute global window methods like `alert()`, `confirm()`, `prompt()`
- **Page Automation**: Navigate, interact with elements, take screenshots
- **JavaScript Execution**: Run custom JavaScript code in browser context

## Prerequisites

1. **Node.js 20.9.0+** - Required for the project
2. **Browser with Remote Debugging** - Chrome/Firefox with remote debugging enabled
3. **Dependencies** - All required packages are already installed

## Starting a Browser with Remote Debugging

### Chrome
```bash
# Windows
chrome --remote-debugging-port=9222 --user-data-dir="./chrome-temp"

# macOS
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222 --user-data-dir="./chrome-temp"

# Linux
google-chrome --remote-debugging-port=9222 --user-data-dir="./chrome-temp"
```

### Firefox
```bash
firefox --remote-debugging-port=9222
```

### Using the Helper Script
```bash
npm run browser
```

## Available Playwright Tools

### 1. `connectBrowser`
Connect to a browser using WebSocket endpoint.

```typescript
import { connectBrowser } from '../src/mastra/tools/playwright-tool';

const result = await connectBrowser.execute({
  input: {
    wsEndpoint: 'ws://localhost:9222/devtools/browser/your-browser-id'
  }
});
```

### 2. `executeWindowMethod`
Execute global window methods like alert, confirm, prompt.

```typescript
import { executeWindowMethod } from '../src/mastra/tools/playwright-tool';

// Alert example
const alertResult = await executeWindowMethod.execute({
  input: {
    method: 'alert',
    args: ['Hello World!']
  }
});

// Confirm example
const confirmResult = await executeWindowMethod.execute({
  input: {
    method: 'confirm',
    args: ['Do you want to continue?']
  }
});

// Prompt example
const promptResult = await executeWindowMethod.execute({
  input: {
    method: 'prompt',
    args: ['Enter your name:', 'Default Name']
  }
});
```

### 3. `navigateToUrl`
Navigate to a specific URL.

```typescript
import { navigateToUrl } from '../src/mastra/tools/playwright-tool';

const result = await navigateToUrl.execute({
  input: {
    url: 'https://example.com'
  }
});
```

### 4. `getPageInfo`
Get page information including URL, title, and element details.

```typescript
import { getPageInfo } from '../src/mastra/tools/playwright-tool';

// Get basic page info
const pageInfo = await getPageInfo.execute({
  input: {}
});

// Get specific element info
const elementInfo = await getPageInfo.execute({
  input: {
    selector: '#my-element'
  }
});
```

### 5. `executeJavaScript`
Execute custom JavaScript code in the browser context.

```typescript
import { executeJavaScript } from '../src/mastra/tools/playwright-tool';

const result = await executeJavaScript.execute({
  input: {
    code: `
      // Get page data
      const data = {
        title: document.title,
        url: window.location.href,
        userAgent: navigator.userAgent
      };
      
      // Modify the page
      const div = document.createElement('div');
      div.textContent = 'Hello from Playwright!';
      document.body.appendChild(div);
      
      return data;
    `
  }
});
```

### 6. `waitForElement`
Wait for an element to appear on the page.

```typescript
import { waitForElement } from '../src/mastra/tools/playwright-tool';

const result = await waitForElement.execute({
  input: {
    selector: '.loading-spinner',
    timeout: 10000  // 10 seconds
  }
});
```

### 7. `clickElement`
Click on an element.

```typescript
import { clickElement } from '../src/mastra/tools/playwright-tool';

const result = await clickElement.execute({
  input: {
    selector: '#submit-button'
  }
});
```

### 8. `typeText`
Type text into an input element.

```typescript
import { typeText } from '../src/mastra/tools/playwright-tool';

const result = await typeText.execute({
  input: {
    selector: '#username-input',
    text: 'john_doe',
    clear: true  // Clear existing text first
  }
});
```

### 9. `takeScreenshot`
Take a screenshot of the page or specific element.

```typescript
import { takeScreenshot } from '../src/mastra/tools/playwright-tool';

// Full page screenshot
const fullPageResult = await takeScreenshot.execute({
  input: {
    fullPage: true,
    path: './screenshots/full-page.png'
  }
});

// Element screenshot
const elementResult = await takeScreenshot.execute({
  input: {
    selector: '#my-element',
    path: './screenshots/element.png'
  }
});
```

### 10. `disconnectBrowser`
Disconnect from the browser.

```typescript
import { disconnectBrowser } from '../src/mastra/tools/playwright-tool';

const result = await disconnectBrowser.execute({
  input: {}
});
```

## Using the Playwright Agent

The Playwright Agent provides a natural language interface to all browser automation capabilities.

### Basic Usage

```typescript
import { playwrightAgent } from '../src/mastra/agents/playwright-agent';

const response = await playwrightAgent.generate([
  {
    role: 'user',
    content: 'Connect to browser at ws://localhost:9222/devtools/browser/abc123 and navigate to google.com'
  }
]);

console.log(response.text);
```

### Example Conversations

#### Browser Navigation
```typescript
const response = await playwrightAgent.generate([
  {
    role: 'user',
    content: `
      Please help me:
      1. Connect to browser using the default WebSocket endpoint
      2. Navigate to https://httpbin.org/forms/post
      3. Fill out the form with test data
      4. Take a screenshot
      5. Don't submit the form
    `
  }
]);
```

#### Window Methods Testing
```typescript
const response = await playwrightAgent.generate([
  {
    role: 'user',
    content: `
      Test various window methods:
      1. Show an alert saying "Testing window methods"
      2. Ask for confirmation with "Continue with the test?"
      3. Prompt for user's favorite color
      4. Get current page location information
    `
  }
]);
```

#### Custom JavaScript Execution
```typescript
const response = await playwrightAgent.generate([
  {
    role: 'user',
    content: `
      Execute JavaScript to:
      1. Change the page background color to light blue
      2. Add a floating notification saying "Automated by Playwright Agent"
      3. Log all form elements on the page
      4. Return the page dimensions
    `
  }
]);
```

## Running Examples

### Tool Examples
```bash
# Run the basic tool examples
npm run example:playwright
```

### Agent Examples
```bash
# Run the agent examples
npm run example:playwright-agent
```

### Start Browser Helper
```bash
# Start a browser with remote debugging
npm run browser
```

## WebSocket Endpoint Discovery

When you start a browser with remote debugging, you can find available WebSocket endpoints:

```bash
# Get list of available endpoints
curl http://localhost:9222/json
```

This returns a JSON array with WebSocket URLs you can use to connect.

## Error Handling

All tools return structured responses with success/failure indicators:

```typescript
interface ToolResponse {
  success: boolean;
  message: string;
  // Additional tool-specific data
}
```

### Common Error Scenarios

1. **Browser Not Connected**: Ensure you call `connectBrowser` first
2. **Invalid WebSocket Endpoint**: Check the endpoint URL and port
3. **Element Not Found**: Verify CSS selectors are correct
4. **Navigation Timeout**: Increase timeout or check network connectivity
5. **JavaScript Errors**: Validate JavaScript syntax before execution

## Best Practices

### 1. Connection Management
```typescript
// Always connect first
await connectBrowser.execute({ input: { wsEndpoint: 'ws://...' } });

// Do your automation work
await navigateToUrl.execute({ input: { url: 'https://example.com' } });
await clickElement.execute({ input: { selector: '#button' } });

// Always disconnect when done
await disconnectBrowser.execute({ input: {} });
```

### 2. Wait for Elements
```typescript
// Wait for elements before interacting
await waitForElement.execute({ 
  input: { 
    selector: '#dynamic-button',
    timeout: 5000 
  } 
});

await clickElement.execute({ 
  input: { selector: '#dynamic-button' } 
});
```

### 3. Error Recovery
```typescript
try {
  const result = await clickElement.execute({
    input: { selector: '#button' }
  });
  
  if (!result.success) {
    console.log('Click failed:', result.message);
    // Handle failure appropriately
  }
} catch (error) {
  console.error('Tool execution error:', error);
}
```

### 4. Screenshot Documentation
```typescript
// Take screenshots at key points
await takeScreenshot.execute({
  input: {
    path: `./screenshots/step-${Date.now()}.png`,
    fullPage: true
  }
});
```

## Advanced Usage

### Custom Browser Configuration
```typescript
// You can connect to browsers running on different ports or hosts
await connectBrowser.execute({
  input: {
    wsEndpoint: 'ws://remote-host:9222/devtools/browser/id'
  }
});
```

### Complex JavaScript Execution
```typescript
await executeJavaScript.execute({
  input: {
    code: `
      // Complex page manipulation
      const results = [];
      
      // Get all links
      const links = Array.from(document.querySelectorAll('a'));
      results.push({ links: links.length });
      
      // Modify page styles
      document.body.style.fontFamily = 'Arial, sans-serif';
      document.body.style.backgroundColor = '#f0f0f0';
      
      // Add custom CSS
      const style = document.createElement('style');
      style.textContent = '.highlight { background: yellow; }';
      document.head.appendChild(style);
      
      // Apply highlighting to all paragraphs
      document.querySelectorAll('p').forEach(p => {
        p.classList.add('highlight');
      });
      
      return results;
    `
  }
});
```

### Form Automation
```typescript
// Complete form filling example
await waitForElement.execute({ 
  input: { selector: '#contact-form' } 
});

await typeText.execute({
  input: {
    selector: '#name',
    text: 'John Doe',
    clear: true
  }
});

await typeText.execute({
  input: {
    selector: '#email',
    text: '<EMAIL>',
    clear: true
  }
});

await takeScreenshot.execute({
  input: {
    selector: '#contact-form',
    path: './screenshots/filled-form.png'
  }
});
```

## Integration with Mastra

The Playwright tools are fully integrated with the Mastra framework:

- **Memory**: Agent conversations are stored with context
- **Logging**: All tool executions are logged
- **Error Handling**: Consistent error patterns across tools
- **Type Safety**: Full TypeScript support with proper types

## Troubleshooting

### Browser Connection Issues
1. Ensure browser is running with `--remote-debugging-port=9222`
2. Check if port 9222 is available: `netstat -an | grep 9222`
3. Try different WebSocket endpoints from `/json`

### Tool Execution Errors
1. Check browser console for JavaScript errors
2. Verify element selectors exist on the page
3. Ensure proper timing with `waitForElement`

### Performance Issues
1. Use specific selectors instead of broad ones
2. Set reasonable timeouts
3. Take screenshots sparingly for large pages

## Contributing

To extend the Playwright tools:

1. Add new tools in `src/mastra/tools/playwright-tool.ts`
2. Update the agent in `src/mastra/agents/playwright-agent.ts`
3. Add examples in the `examples/` directory
4. Update this documentation

## License

This Playwright integration is part of the testing-agent project and follows the same license terms. 