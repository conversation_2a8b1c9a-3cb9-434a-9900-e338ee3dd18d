{"name": "testing-agent", "version": "1.0.0", "description": "", "main": "index.js", "bin": "dist/stdio.js", "scripts": {"dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start", "browser": "tsx scripts/start-browser.ts", "example:playwright": "tsx examples/playwright-tool-example.ts", "example:playwright-agent": "tsx examples/playwright-agent-example.ts", "build:mcp": "tsup src/mastra/stdio.ts --format esm --no-splitting --dts && chmod +x dist/stdio.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.22", "@mastra/core": "^0.10.10", "@mastra/libsql": "^0.11.0", "@mastra/loggers": "^0.10.3", "@mastra/mcp": "^0.10.5", "@mastra/memory": "^0.11.1", "@openrouter/ai-sdk-provider": "^0.7.2", "@playwright/mcp": "^0.0.29", "playwright": "^1.53.2", "zod": "^3.25.71"}, "devDependencies": {"@types/node": "^24.0.10", "mastra": "^0.10.10", "tsup": "^8.5.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}