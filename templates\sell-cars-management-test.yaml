config:
  name: "奶茶车管理测试"
  description: "奶茶车管理新增和删除功能测试"
  baseUrl: "http://************:9999"
  executorType: "element-plus"
  testMode: "flow"
  timeout: 30000
  variables:
    # 表单数据配置
    testCarData:
      carName: "自动化测试车辆"
      carCode: "AUTO_TEST_001"
      modelNumber: "XH001"
      plateNo: "川A88888"
      deviceMachineCarIdA: ""
      deviceMachineCarIdB: ""
      containerTypeId: ""
      trashCanCount: "100"
      waringPercent: "20"
      runAreaId: ""
    
    # 删除测试数据
    deleteCarData:
      carName: "自动化测试车辆"

templates:
  add-sell-car-template:
    name: "新增车辆模板"
    type: "shared"
    parameters:
      - name: carName
        type: "string"
        required: true
      - name: carCode
        type: "string"
        required: true
      - name: modelNumber
        type: "string"
        required: false
      - name: plateNo
        type: "string"
        required: false
      - name: deviceMachineCarIdA
        type: "string"
        required: false
      - name: deviceMachineCarIdB
        type: "string"
        required: false
      - name: containerTypeId
        type: "string"
        required: false
      - name: trashCanCount
        type: "string"
        required: false
      - name: waringPercent
        type: "string"
        required: false
      - name: runAreaId
        type: "string"
        required: false
    steps:
      # 点击添加按钮
      - action: click
        role: button
        roleOptions:
          name: "添加"
      
      # 等待对话框出现
      - action: wait
        data: 1000
      
      # 填写名称（必填）
      - action: fill
        role: textbox
        roleOptions:
          name: "* 名称"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{carName}}"
      
      # 填写编码（必填）
      - action: fill
        role: textbox
        roleOptions:
          name: "* 编码"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{carCode}}"
      
      # 选择型号（可选）
      - action: selectOption
        role: combobox
        roleOptions:
          name: "型号"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        index: 0
        when: "{{modelNumber}}"
      
      # 填写车牌号（可选）
      - action: fill
        role: textbox
        roleOptions:
          name: "车牌号"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{plateNo}}"
        when: "{{plateNo}}"
      
      # 选择机器A（可选）
      - action: selectOption
        role: combobox
        roleOptions:
          name: "机器A"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        index: 0
        when: "{{deviceMachineCarIdA}}"
      
      # 选择机器B（可选）
      - action: selectOption
        role: combobox
        roleOptions:
          name: "机器B"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        index: 0
        when: "{{deviceMachineCarIdB}}"
      
      # 选择预存柜（可选）
      - action: selectOption
        role: combobox
        roleOptions:
          name: "预存柜"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        index: 0
        when: "{{containerTypeId}}"
      
      # 填写废弃桶容量（可选）
      - action: fill
        role: textbox
        roleOptions:
          name: "请输入容量"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{trashCanCount}}"
        when: "{{trashCanCount}}"
      
      # 填写废弃桶预警值（可选）
      - action: fill
        role: textbox
        roleOptions:
          name: "请输入预警值"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        data: "{{waringPercent}}"
        when: "{{waringPercent}}"
      
      # 选择投放区域（可选）
      - action: selectOption
        role: combobox
        roleOptions:
          name: "投放区域"
        within:
          role: dialog
          roleOptions:
            name: "新增车辆"
        index: 0
        when: "{{runAreaId}}"
      
      # 提交表单
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 2000

  delete-sell-car-template:
    name: "删除车辆模板"
    type: "shared"
    parameters:
      - name: carName
        type: "string"
        required: true
        description: "要删除的车辆名称"
    steps:
      # 使用表格操作脚本查找并删除指定车辆
      - action: useScript
        script: "table-operations"
        parameters:
          action: "deleteRow"
          fields:
            - column: "名称"
              value: "{{carName}}"
              exact: true
          timeout: 3000
      
      # 确认删除操作
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"
          timeout: 2000

tests:
  - name: "奶茶车管理完整流程测试"
    description: "测试新增车辆和删除车辆的完整流程"
    steps:
      # 导航到页面
      - action: navigate
        url: "/#/deviceManage/sellCarsManage/index"
      
      # 等待页面加载
      - action: wait
        data: 2000
      
      # 使用新增模板添加车辆
      - action: useTemplate
        template: "add-sell-car-template"
        parameters: "{{testCarData}}"
      
      # 等待操作完成
      - action: wait
        data: 3000
      
      # 验证车辆是否添加成功
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testCarData.carName}}"
        assertion: visible
      
      # 使用删除模板删除车辆
      - action: useTemplate
        template: "delete-sell-car-template"
        parameters: "{{deleteCarData}}"
      
      # 等待删除完成
      - action: wait
        data: 2000
      
      # 验证车辆是否删除成功（应该不可见）
      - action: verify
        type: element
        role: cell
        roleOptions:
          name: "{{testCarData.carName}}"
        assertion: hidden